import type { Response, Router } from "express";

const express = require("express");
const { create } = require("xmlbuilder2");

const responseHelper = require("../helpers/response");

const projectsController = require("./1.0/projects");
const corpusController = require("./1.0/corpus");
const userController = require("./1.0/user");
const tempuserController = require("./1.0/tempuser");
const treeController = require("./1.0/tree");
const conservatoire3dController = require("./1.0/conservatoire3d");
const objectController = require("./1.0/object");
const metadataController = require("./1.0/metadata");
const fileController = require("./1.0/file");
const folderController = require("./1.0/folder");
const fsController = require("./1.0/fs");
const thesaurusController = require("./1.0/thesaurus");
const entityController = require("./1.0/physicalEntity");
const actorController = require("./1.0/actor");
const tagController = require("./1.0/tag");
const groupController = require("./1.0/group");
const archeogridController = require("./1.0/archeogrid");
const repositoryController = require("./1.0/3drepository");
const jsonController = require("./1.0/json");
const tableController = require("./1.0/table");
const searchController = require("./1.0/search");
const xmlController = require("./1.0/xml");
const commentController = require("./1.0/comment");
const doiController = require("./1.0/doi");
const csvController = require("./1.0/csv");
const collectionController = require("./1.0/collection");
const unicoController = require("./1.0/unico");
const oaiController = require("./1.0/oai");
const favoritesController = require("./1.0/favorites");

const router: Router = express.Router();

const renderXml = (obj: unknown) => create({ version: "1.0", encoding: "utf-8" }).ele('response').ele(obj).end({ prettyPrint: true });

type ExtendedResponse = Response & Record<string, unknown>;

router.use((req, res: ExtendedResponse, next) => {
  res.sendData = (obj: unknown) => {
    if (req.accepts("json") || req.accepts("text/html")) {
      res.header("Content-Type", "application/json");
      res.send(obj);
    } else if (req.accepts("application/xml")) {
      res.header("Content-Type", "text/xml");
      const xml = renderXml(obj);
      res.send(xml);
    } else {
      res.send(406);
    }
  };

  next();
});

/**
 *
  ____            _           _
 |  _ \ _ __ ___ (_) ___  ___| |_ ___
 | |_) | '__/ _ \| |/ _ \/ __| __/ __|
 |  __/| | | (_) | |  __/ (__| |_\__ \
 |_|   |_|  \___// |\___|\___|\__|___/
               |__/
 *
 */
router
  .route("/projects")
  .all(function (req, res, next) {
    if (req.method !== "GET" && req.method !== "PUT") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(projectsController.getProjects)
  .put(projectsController.createProject);

router
  .route("/projectsOverall")
  .all(function (req, res, next) {
    if (req.method !== "GET" && req.method !== "PUT") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(projectsController.getOverallProjects)
  .put(projectsController.createOverallProject);

router
  .route("/projectsCard")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(projectsController.getProjectsCard);

router
  .route("/projectsCard/:classement")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(projectsController.getProjectsCardClass);

router
  .route("/projectsMap")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(projectsController.getProjectsMap);

router
  .route("/projectsFull")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(projectsController.getProjectsFull);

router
  .route("/projectFull/:root,:idProject,:lang")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(projectsController.getProjectFull);

router
  .route("/overallProjectFull/:branche,:idOverallProject,:lang")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(projectsController.getOverallProjectFull);

router
  .route("/projectsRoot/:root")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(projectsController.getProjectsRootList);

router
  .route("/limitedProjectsRoot/:root")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(projectsController.getLimitedProjectsRootList);

router
  .route("/projectsRootName/:branch,:name")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(projectsController.getProjectsRootName);

router
  .route("/projectName/:branch,:idProject")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(projectsController.getProjectName);

router
  .route("/getProjectId/:branch,:idFile")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(projectsController.getProjectId);

router
  .route("/overallProject/:branche,:idOverallProject")
  .all(function (req, res, next) {
    if (req.method !== "GET" && req.method !== "PATCH" && req.method !== "DELETE") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(projectsController.getProjectsIdFromOverall)
  .patch(projectsController.linkProjectsIdFromOverall)
  .delete(projectsController.deleteProjectIdSiteFromOverall);

router
  .route("/projectWithOverall/:branche,:idProject")
  .all(function (req, res, next) {
    if (req.method !== "GET" && req.method !== "PATCH" && req.method !== "DELETE") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(projectsController.getOverallFromProject)
  .patch(projectsController.linkOverallToProject)
  .delete(projectsController.deleteOverallProjectLink);

router
  .route("/projectWithOverallDetail/:branche,:idProject")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(projectsController.getOverallDetailsFromProject);

router
  .route("/assignedProjects/:branche,:idUserScribe")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(projectsController.getListProjectFromUser);
//    .put(projectsController.l)

/**
 *   _   _ ___  ___ _ __
 *  | | | / __|/ _ \ '__|
 *  | |_| \__ \  __/ |
 *  \__,_|___/\___|_|
 *
 */
router
  .route("/userAccess/:userId,:root")
  .all(function (req, res, next) {
    if (req.method != "GET" && req.method != "PUT") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(userController.getUserAccess)
  .put(userController.createUserAccess);

router
  .route("/userAccessWrite/:userId,:root")
  .all(function (req, res, next) {
    if (req.method != "GET" && req.method != "PUT") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(userController.getUserAccessWrite)
  .put(userController.createUserAccessWrite);

router
  .route("/scribeAccessAdminlistSubFolders/:branche,:idFolder,:idUser")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(userController.getListSubFoldersFromIdfolderGiveRights);

router
  .route("/userAccessAdmin/:userId,:root")
  .all(function (req, res, next) {
    if (req.method != "GET" && req.method != "PUT" && req.method != "DELETE") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(userController.getUserAccessAdmin)
  .put(userController.createUserAccessAdmin)
  .delete(userController.deleteUserAccessAdmin);

router
  .route("/userWriteAccessAdmin/:userId,:root")
  .all(function (req, res, next) {
    if (req.method != "GET" && req.method != "PUT" && req.method != "DELETE") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(userController.getUserWriteAccessAdmin)
  .put(userController.createUserWriteAccessAdmin)
  .delete(userController.deleteUserWriteAccessAdmin);

router
  .route("/allUserGroup")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(userController.getAllUserGroup);

router
  .route("/userForbiddenfolders/:userId,:root")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(userController.getUserForbiddenFolders);

router
  .route("/userOnlyForbiddenfolders/:userId,:root")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(userController.getUserOnlyForbiddenFolders);

router
  .route("/limitedUserOnlyForbiddenfolders/:userId,:branche")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(userController.getLimitedUserOnlyForbiddenFolders);

router
  .route("/userAuthorizedFolders/:userId,:root")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(userController.getUserAuthorizedFolders);

router
  .route("/userAuthorizedProjectFolders/:branche,:userId")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(userController.getUserAuthorizedProjectFolders);

router
  .route("/userWriteAuthorizedFolders/:userId,:root")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(userController.getUserWriteAuthorizedFolders);

router
  .route("/scribeWriteAuthorizedFolders/:userId,:root")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(userController.getScribeAuthorizedFolders);

router
  .route("/userAuthorizedFoldersWithPublic/:userId,:root")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(userController.getUserAuthorizedFoldersWithPublic);

router
  .route("/userTotalAuthorizedFoldersWithPublic/:userId,:root")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(userController.getUserTotalAuthorizedFoldersWithPublic);

router
  .route("/userAccessFolder/:userId,:folderId,:root")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(userController.getUserAccessFolder);

router
  .route("/userAccessFolderGlobal/:userId,:folderId,:root")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(userController.getUserAccessFolderGlobal);

router
  .route("/userAccessFile/:userId,:fileId,:folderId,:branch")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(userController.getUserAccessFile);

router
  .route("/userAccessFileCND3D/:userId,:fileId,:branch")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(userController.getUserAccessFileCND3D);

router
  .route("/userSession")
  .all(function (req, res, next) {
    if (req.method !== "GET" && req.method !== "PUT") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(userController.getUserSession)
  .put(userController.createUserSession);

router
  .route("/userSessionOIDC")
  .all(function (req, res, next) {
    if (req.method != "PUT") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  //.get(userController.getUserSessionOIDC)
  .put(userController.createUserSessionOIDC);

router
  .route("/userEntity/:userId")
  .all(function (req, res, next) {
    if (req.method != "GET" && req.method != "PUT") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(userController.getUserEntity)
  .put(userController.createUserEntity);

router
  .route("/users,:order")
  .all(function (req, res, next) {
    if (req.method != "GET" && req.method != "POST") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(userController.getUsers)
  .post(userController.createUser);

router
  .route("/user/:userId")
  .all(function (req, res, next) {
    if (req.method !== "GET" && req.method !== "PATCH" && req.method !== "DELETE") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(userController.getUser)
  .patch(userController.updateUser)
  .delete(userController.deleteUser);

router
  .route("/scribeUsers/:order")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(userController.getSribeUsers);

router
  .route("/usersCreatedByScribe/:branche,:scribeId,:order")
  .all(function (req, res, next) {
    if (req.method !== "GET" && req.method !== "PUT") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(userController.getUsersCreatedBySribe)
  .put(userController.linkScribeUser);

router
  .route("/projectsUsers/:branche,:userId,:order")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(userController.getProjectsUsers);

router
  .route("/userStatus")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(userController.getUserStatus);

router
  .route("/userRightsFolders/:branche,:userId")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(userController.getRightsFoldersUser);

router
  .route("/scribeUserLinkProject/:root,:userId,:folderId")
  .all(function (req, res, next) {
    if (req.method !== "PUT") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .put(userController.linkScribeProject);

router
  .route("/userLinkProjects/:root,:userId")
  .all(function (req, res, next) {
    if (req.method !== "PUT") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .put(userController.linkUserProjects);

/**
 *    _                       _   _
 *   | |_ ___ _ __ ___  _ __ | | | |___  ___ _ __
 *  | __/ _ \ '_ ` _ \| '_ \| | | / __|/ _ \ '__|
 *  | ||  __/ | | | | | |_) | |_| \__ \  __/ |
 *  \__\___|_| |_| |_| .__/ \___/|___/\___|_|
 *                   |_|
 *
 **/
router
  .route("/tempuser")
  .all(function (req, res, next) {
    if (req.method != "GET" && req.method != "PUT") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(tempuserController.tempUser)
  .put(tempuserController.createTempUser);

router
  .route("/tempuser/:userId")
  .all(function (req, res, next) {
    if (req.method != "GET" && req.method != "PATCH" && req.method != "DELETE") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(tempuserController.getTempUser)
  .patch(tempuserController.updateTempUser)
  .delete(tempuserController.deleteTempUser);

router
  .route("/tempuser/link/:userId")
  .all(function (req, res, next) {
    if (req.method != "PATCH") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .patch(tempuserController.updateLinkTempUser);

/***
 *   _____       _   _ _
 *  | ____|_ __ | |_(_) |_ _   _
 *  |  _| | '_ \| __| | __| | | |
 *  | |___| | | | |_| | |_| |_| |
 *  |_____|_| |_|\__|_|\__|\__, |
 *                         |___/
 *
 */
router
  .route("/entity")
  .all(function (req, res, next) {
    if (req.method != "GET" && req.method != "PUT") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(entityController.getPhysicalEntity)
  .put(entityController.createPhysicalEntity);

router
  .route("/entity/:idEntity")
  .all(function (req, res, next) {
    if (req.method != "GET" && req.method != "PATCH" && req.method != "PUT" && req.method != "DELETE") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(entityController.getSingleEntity)
  .patch(entityController.patchEntity)
  .put(entityController.createEntity)
  .delete(entityController.deleteEntity);

router
  .route("/entityFromFolder/:folderName")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(entityController.getEntityFromFolder);

router
  .route("/entityFolder/:idUser")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(entityController.getFolderEntityFromUser);

router
  .route("/deposantWithoutIdthes/:branche")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(entityController.getEntityWithoutidThes);

router
  .route("/deposantNameLinkThes/:branche")
  .all(function (req, res, next) {
    if (req.method !== "GET" && req.method !== "PATCH") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(entityController.entityNameGetFromidThes)
  .patch(entityController.entityNameLinkidThes);

/**
 *     _        _
 *    / \   ___| |_ ___  _ __
 *   / _ \ / __| __/ _ \| '__|
 *  / ___ \ (__| || (_) | |
 * /_/   \_\___|\__\___/|_|
 *
 *
 */
router
  .route("/actor/:branche,:idActor")
  .all(function (req, res, next) {
    if (req.method !== "GET" && req.method !== "PATCH" && req.method !== "DELETE") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(actorController.getActor)
  .patch(actorController.updateActor)
  .delete(actorController.delActor);

router
  .route("/actor/:branche")
  .all(function (req, res, next) {
    if (req.method !== "PUT") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .put(actorController.createActor);

router
  .route("/actors/:branche,:column,:order")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(actorController.getallActors);

router
  .route("/personWithoutIdthes/:branche")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(actorController.getPersonWithoutidThes);

router
  .route("/personNameLinkThes/:branche")
  .all(function (req, res, next) {
    if (req.method !== "GET" && req.method !== "PATCH") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(actorController.personGetFromidThes)
  .patch(actorController.personNameLinkidThes);

/**
 *
 *   ___                        _          _   _
 *  / _ \ _ __ __ _  __ _ _ __ (_)______ _| |_(_) ___  _ __
 * | | | | '__/ _` |/ _` | '_ \| |_  / _` | __| |/ _ \| '_ \
 * | |_| | | | (_| | (_| | | | | |/ / (_| | |_| | (_) | | | |
 *  \___/|_|  \__, |\__,_|_| |_|_/___\__,_|\__|_|\___/|_| |_|
 *            |___/
 */
router
  .route("/organization/:branche,:idOrg")
  .all(function (req, res, next) {
    if (req.method !== "GET" && req.method !== "PATCH" && req.method !== "DELETE") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(actorController.getOrganization)
  .patch(actorController.updateOrganization)
  .delete(actorController.deleteOrganization);

router
  .route("/organization/:branche")
  .all(function (req, res, next) {
    if (req.method !== "PUT") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .put(actorController.createOrganization);

router
  .route("/organizations,:branche,:column,:order")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(actorController.getOrganizationsOrder);

router
  .route("/organizationWithoutIdthes/:branche")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(actorController.getOrganizationWithoutidThes);

router
  .route("/organizationNameLinkThes/:branche")
  .all(function (req, res, next) {
    if (req.method !== "GET" && req.method !== "PATCH") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(actorController.organizationGetFromidThes)
  .patch(actorController.organizationNameLinkidThes);

/**
 *  ____      _ _           _   _
  / ___|___ | | | ___  ___| |_(_) ___  _ __
 | |   / _ \| | |/ _ \/ __| __| |/ _ \| '_ \
 | |__| (_) | | |  __/ (__| |_| | (_) | | | |
 \____\___/|_|_|\___|\___|\__|_|\___/|_| |_|
 *
 */
router
  .route("/collections/:branche")
  .all(function (req, res, next) {
    if (req.method !== "GET" && req.method !== "PUT") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(collectionController.getCollection)
  .put(collectionController.createCollection);

router
  .route("/collection/:branche,:idCollection")
  .all(function (req, res, next) {
    if (req.method !== "GET" && req.method !== "PATCH" && req.method !== "DELETE") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(collectionController.getSingleCollection)
  .patch(collectionController.patchCollection)
  .delete(collectionController.deleteCollection);

router
  .route("/collectionUser/:branche,:idUser")
  .all(function (req, res, next) {
    if (req.method !== "GET" && req.method !== "PUT") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(collectionController.getCollectionFromUser)
  .put(collectionController.createLinkCollectionUser);

router
  .route("/heritageAssets/:branche")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(collectionController.getheritageAssets);

router
  .route("/heritageAsset/:branche,:id")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(collectionController.getheritageAssetFromId);

router
  .route("/heritageAssetFile/:branche,:idHA")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(collectionController.getFileFromheritageAssetId);

router
  .route("/activity/:branche,:idAC")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(collectionController.getActivity);

/**
 *
 *_____     _     _
 |_   _|_ _| |__ | | ___
   | |/ _` | '_ \| |/ _ \
   | | (_| | |_) | |  __/
   |_|\__,_|_.__/|_|\___|
 *
 */
router
  .route("/columnTable/:table")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(tableController.getTableColumn);

router
  .route("/columnTableLabel/:branche,:table")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(tableController.getTableColumnLabel);

/**
 *
 *    _____
 *  |_   _| __ ___  ___
 *   | || '__/ _ \/ _ \
 *   | || | |  __/  __/
 *  |_||_|  \___|\___|
 *
 */
/**
 * STILL UNUSED
 */
router
  .route("/tree/:root")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(treeController.getTree);

/**
 * STILL UNUSED
 */
router
  .route("/singleTree/:folder_id,:root")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(treeController.getSingleTree);

/**
 * STILL UNUSED
 */
router
  .route("/children/:folder_id,:root")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(treeController.getChildren);

router
  .route("/csstree/:folder_id,:root")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(treeController.getcssTree);

router
  .route("/csstreeBOOL/:folder_id,:root")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(treeController.getcssTreeBool);

router
  .route("/csstreeBOOLUser/:folder_id,:idUser,:root")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(treeController.getcssTreeBoolUser);

/**
 *
 *   ____                                     _        _          _____     _
 *  / ___|___  _ __  ___  ___ _ ____   ____ _| |_ ___ (_)_ __ ___|___ /  __| |
 * | |   / _ \| '_ \/ __|/ _ \ '__\ \ / / _` | __/ _ \| | '__/ _ \ |_ \ / _` |
 * | |__| (_) | | | \__ \  __/ |   \ V / (_| | || (_) | | | |  __/___) | (_| |
 * \____\___/|_| |_|___/\___|_|    \_/ \__,_|\__\___/|_|_|  \___|____/ \__,_|
 *
 *
 */
router
  .route("/depots")
  .all(function (req, res, next) {
    if (req.method != "GET" && req.method != "PUT") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(conservatoire3dController.getDepots)
  .put(conservatoire3dController.createDepot);

router
  .route("/depotsObjectsMap/:lng")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(conservatoire3dController.getDepotsObjectsMap);

router
  .route("/depotsMapNB")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(conservatoire3dController.getDepotsMapNB);

router
  .route("/nbDepots")
  .all(function (req, res, next) {
    if (req.method != "GET" && req.method != "PUT") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(conservatoire3dController.getNBDepots);

router
  .route("/depot/:depotId,:lang")
  .all(function (req, res, next) {
    if (req.method != "GET" && req.method != "PUT") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  //.get(conservatoire3dController.getDepotFullOld)
  .get(conservatoire3dController.getDepotFull)
  .put(conservatoire3dController.createDepotUserLink);

router
  .route("/depotName/:depot_name")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(conservatoire3dController.getDepotName);

router
  .route("/depotInfoSize/:root,:depotId")
  .all(function (req, res, next) {
    if (req.method !== "GET" && req.method !== "PUT") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(conservatoire3dController.getDepotInfoSize)
  .put(conservatoire3dController.createDepotInfoSize);

router
  .route("/depotsListId")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(conservatoire3dController.getDepotListId);

router
  .route("/deposant/:idFolder")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(conservatoire3dController.getDeposant);

router
  .route("/ListIdFilesForDOI")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(conservatoire3dController.getListFilesIdForDOI);

router
  .route("/depotsUser/:idUser")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(conservatoire3dController.getDepotsUser);

router
  .route("/depotsObjectsUser/:idUser")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(conservatoire3dController.getDepotsObjectsUser);

router
  .route("/depotInfoLight/:idFolder")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(conservatoire3dController.getDepotInfolight);

router
  .route("/SitesFromCodeName/:branche,:codeName")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(conservatoire3dController.getSitesFromCodeName);

/**
 *        _     _           _
 *  ___ | |__ (_) ___  ___| |_
 * / _ \| '_ \| |/ _ \/ __| __|
 *| (_) | |_) | |  __/ (__| |_
 * \___/|_.__// |\___|\___|\__|
 *          |__/
 *
 */
router
  .route("/object/:root")
  .all(function (req, res, next) {
    if (req.method !== "GET" && req.method !== "PUT" && req.method !== "PATCH" && req.method !== "DELETE") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(objectController.getObject)
  .put(objectController.createObject)
  .patch(objectController.linkObject)
  .delete(objectController.deleteObject);

router
  .route("/itemsFromObject/:branche,:idObject")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(objectController.getItemsFromObject);

router
  .route("/folderFromObject/:root,:idObject")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(objectController.getFolderFromObject);

router
  .route("/objectLinkFolder/:root,:idObject")
  .all(function (req, res, next) {
    if (req.method != "GET" && req.method != "PUT" && req.method != "DELETE") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(objectController.getLinkFolderFromObject)
  .put(objectController.createLinkFolderFromObject)
  .delete(objectController.deleteLinkFolderFromObject);

router
  .route("/virtualObjectLink/:branche,:idUser,:idObject")
  .all(function (req, res, next) {
    if (req.method != "PUT") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .put(objectController.createVirtualObjectLink);

router
  .route("/objectIllustrate/:branche,:idObject")
  .all(function (req, res, next) {
    if (req.method !== "PUT") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .put(objectController.createObjectIllustration);

router
  .route("/exploreObj/:root,:idFolder,:lang")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(objectController.getObjectFromFolder);

router
  .route("/explore3dObj/:branch,:idUser")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(objectController.getObjectWith3dinfo);

router
  .route("/exploreObjV/:root,:idFolder,:lang,:idUser")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(objectController.getObjectVirtuelFromFolder);

router
  .route("/prepareObject/:idObject,:root")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(objectController.getPrepareObject);

router
  .route("/ObjectReprFile/:branche,:idObject")
  .all(function (req, res, next) {
    if (req.method !== "GET" && req.method !== "PUT" && req.method !== "PATCH" && req.method !== "DELETE") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(objectController.getReprFileObject)
  .put(objectController.putReprFileObject)
  .patch(objectController.patchReprFileObject)
  .delete(objectController.deleteReprFileObject);

router
  .route("/objectId/:branche,:idObject")
  .all(function (req, res, next) {
    if (req.method !== "GET" && req.method !== "PATCH") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(objectController.getObjectFromId)
  .patch(objectController.changeObjectValue);

router
  .route("/objectViewer/:branche,:idObject")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(objectController.getObjectViewerFromId);

router
  .route("/objectsFolder/:branche,:idFolder")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(objectController.getObjectsFromidFolder);

router
  .route("/objectLinkUser/:branch,:idObject")
  .all(function (req, res, next) {
    if (req.method !== "GET" && req.method !== "PUT" && req.method !== "DELETE") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(objectController.getLinkObjectUser)
  .put(objectController.linkUserObject)
  .delete(objectController.deleteLinkUserFromObject);

/**
 *  _____ _ _
 * |  ___(_) | ___
 * | |_  | | |/ _ \
 * |  _| | | |  __/
 * |_|   |_|_|\___|
 *
 */
router
  .route("/objectsFromFile/:file_id,:root")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(fileController.getObjectFromFile);

router
  .route("/prepareImage/:file_id,:root")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(fileController.getPrepareImage);

router
  .route("/moreInfoImage/:file_id,:root")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(fileController.getMoreInfoImage);

router
  .route("/IPTC_EXIFCode/:root,:lang")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(fileController.getIPTC_EXIFCode);

router
  .route("/generateMini")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(fileController.getMini);

router
  .route("/fileFromId/:branche,:idFile")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(fileController.getFileFromIdFile);

router
  .route("/folderFromFile/:root,:idFile")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(fileController.getFolderFromFile);

router
  .route("/favorites/:branche,:idUser,:idFolder")
  .all(function (req, res, next) {
    if (req.method != "GET" && req.method != "PUT" && req.method != "DELETE") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(fileController.getFavoriteFileFromRootFolder)
  .put(fileController.createfavorite)
  .delete(fileController.deletefavorite);

router
  .route("/favoritesItems/:branche,:idUser,:idFolder")
  .all(function (req, res, next) {
    if (req.method !== "GET" && req.method !== "PUT" && req.method !== "DELETE") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(fileController.getFavoriteItemFromRootFolder)
  .put(fileController.createfavoriteItem)
  .delete(fileController.deletefavoriteItem);

router
  .route("/favoriteItem/:branche,:idUser,:idFolder,:idItem,:typeItem")
  .all(function (req, res, next) {
    if (req.method !== "DELETE") {
      responseHelper.sendError(501, "not_implemented", req.method + " method is not yet implemented", req, res);
    } else {
      next();
    }
  })
  .delete(favoritesController.deleteSingleFavoriteItem);

router
  .route("/userFavoritesItems/:branche,:idUser")
  .all(function (req, res, next) {
    if (req.method !== "GET" && req.method !== "PUT" && req.method !== "DELETE") {
      responseHelper.sendError(501, "not_implemented", req.method + " method is not yet implemented", req, res);
    } else {
      next();
    }
  })
  .get(favoritesController.getFavoriteItemFromUser)
  .delete(favoritesController.deleteUserFavoriteItem);

router
  .route("/exploreFavoritesNB/:branche,:idUser,:lng")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", req.method + " method is not yet implemented", req, res);
    } else {
      next();
    }
  })
  .get(favoritesController.exploreFavoritesNB);

router
  .route("/exploreFavPage/:branche")
  .all(function (req, res, next) {
    if (req.method !== "POST") {
      responseHelper.sendError(501, "not_implemented", req.method + " method is not yet implemented", req, res);
    } else {
      next();
    }
  })
  .post(favoritesController.exploreFavoritesPage);

router
  .route("/hash/:branche,:hash")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(fileController.getFileFromHash);

router
  .route("/projectExtensions/:branche,:idFolder,:idUser")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(fileController.getProjectExtensions);

router
  .route("/fileViewer/:branche,:idFile")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(fileController.getFileViewer);

router
  .route("/3DFromFile/:branche,:idFile")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(fileController.get3DFileFromFile);

/**
 *  _    _       _
 * | |  | |     (_)
 * | |  | |_ __  _  ___ ___
 * | |  | | '_ \| |/ __/ _ \
 * | |__| | | | | | (_| (_) |
 *  \____/|_| |_|_|\___\___/
 *
 */

router
  .route("/createUnico/:branch,:thesaurus")
  .all(function (req, res, next) {
    if (req.method !== "POST") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .post(unicoController.createUnico);

router
  .route("/updateUnico/:branch,:thesaurus")
  .all(function (req, res, next) {
    if (req.method !== "POST") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .post(unicoController.updateUnico);

router
  .route("/getUnico/:branch,:id")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(unicoController.getUnico);

router
  .route("/deleteUnico/:branch,:id")
  .all(function (req, res, next) {
    if (req.method != "DELETE") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .delete(unicoController.deleteUnico);

router
  .route("/prepareDeleteUnico/:branch,:id")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(unicoController.prepareDeleteUnico);

router
  .route("/getFileUnicos/:branch,:idFile,:lng,:idProject")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(unicoController.getFileUnicos);

router
  .route("/getUserUnicos/:branch,:idUser")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(unicoController.getUserUnicos);

router
  .route("/unicosNb/:branch,:idFolder") // obsolete GET
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(unicoController.getUnicosNb);

router
  .route("/unicosPage/:branch,:idFolder")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(unicoController.getUnicosPage);

/**
 *  _____     _     _
 * |  ___|__ | | __| | ___ _ __
 * | |_ / _ \| |/ _` |/ _ \ '__|
 * |  _| (_) | | (_| |  __/ |
 * |_|  \___/|_|\__,_|\___|_|
 *
 */
router
  .route("/folder/:root")
  .all(function (req, res, next) {
    if (req.method != "GET" && req.method != "PUT") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(folderController.getFolder)
  .put(folderController.createFolder);

router
  .route("/rootFolders/:root")
  .all(function (req, res, next) {
    if (req.method != "GET" && req.method != "PATCH" && req.method != "PUT") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(folderController.getRootFolders)
  .patch(folderController.patchRootFoldersOrder)
  .put(folderController.createRootFolder);

router
  .route("/statusFolder/:folderId,:root")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(folderController.getStatusFolder);

router
  .route("/profondeurMaxFolder/:branche,:idFolder")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(folderController.getprofondeurFolder);

router
  .route("/changeFolderOrder/:branche")
  .all(function (req, res, next) {
    if (req.method != "PATCH") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .patch(folderController.changeFolderOrder);

router
  .route("/updateGlobalRankFolderOrder/:branche")
  .all(function (req, res, next) {
    if (req.method != "PATCH") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .patch(folderController.updateGlobalRankFolderOrder);

router
  .route("/explore/:folderId,:root")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(folderController.getFolderItem);

router
  .route("/folderFull/:folderId,:root,:lang")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(folderController.getFolderFull);

router
  .route("/folderSimple/:folderId,:root")
  .all(function (req, res, next) {
    if (req.method != "GET" && req.method != "PATCH") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(folderController.getFolderSimple)
  .patch(folderController.setFolderSimple);

router
  .route("/privateFolders/:root")
  .all(function (req, res, next) {
    if (req.method !== "GET" && req.method !== "PATCH") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(folderController.getPrivateFolders)
  .patch(folderController.setPrivateFolders);

router
  .route("/limitedPrivateFolders/:root")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(folderController.getLimitedPrivateFolders);

router
  .route("/publicFolders/:root")
  .all(function (req, res, next) {
    if (req.method !== "GET" && req.method !== "PATCH") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(folderController.getPublicFolders)
  .patch(folderController.setPublicFolders);

router
  .route("/limitedPublicFolders/:root")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(folderController.getLimitedPublicFolders);
//.patch(folderController.setPublicFolders)

router
  .route("/downloadableFolders/:root")
  .all(function (req, res, next) {
    if (req.method !== "GET" && req.method !== "PATCH") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(folderController.getDownloadableFolders)
  .patch(folderController.setDownloadableFolders);

router
  .route("/nondownloadableFolders/:root")
  .all(function (req, res, next) {
    if (req.method !== "GET" && req.method !== "PATCH") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(folderController.getNonDownloadableFolders)
  .patch(folderController.setNonDownloadableFolders);

router
  .route("/nbImagesFolder/:root,:idFolder")
  .all(function (req, res, next) {
    if (req.method != "GET" && req.method != "PATCH") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(folderController.getNbImagesFolder)
  .patch(folderController.setNbImagesFolder);

router
  .route("/idFolders/:root")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(folderController.getIdFolder);

router
  .route("/idPrivateFolders/:root")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(folderController.getIdPrivateFolder);

router
  .route("/idPublicFolders/:root")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(folderController.getIdPublicFolder);

router
  .route("/mainFolder/:root,:idFolder,:lng")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(folderController.getMainFolder);

router
  .route("/rootFolder/:branche,:idFolder")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(folderController.getRootFolder);

router
  .route("/rootFolderFromFile/:branche,:idFile")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(folderController.getRootFolderFromFile);

router
  .route("/representativeImage/:branche,:idFolder")
  .all(function (req, res, next) {
    if (req.method !== "GET" && req.method !== "PATCH") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(folderController.getRepresentativeImage)
  .patch(folderController.patchRepresentativeImage);

router
  .route("/folderPath/:branche,:idFolder")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(folderController.getFolderPath);

router
  .route("/virtualFolder/:branche,:idParent")
  .all(function (req, res, next) {
    if (req.method !== "GET" && req.method !== "PUT" && req.method !== "DELETE") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(folderController.getVirtualFolder)
  .put(folderController.createVirtualFolder)
  .delete(folderController.deleteVirtualFolder);

router
  .route("/virtualFolderLink/:branche,:idUser,:idVirtualFolder")
  .all(function (req, res, next) {
    if (req.method != "GET" && req.method != "PUT" && req.method != "DELETE") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(folderController.getVirtualFolderLink)
  .put(folderController.createVirtualFolderLink)
  .delete(folderController.deleteVirtualFolderLink);

router
  .route("/virtualFolderContent/:branche,:idVirtualFolder")
  .all(function (req, res, next) {
    if (req.method !== "GET" && req.method !== "DELETE") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(folderController.getVirtualFolderContent)
  .delete(folderController.deleteVirtualFolderContent);

router
  .route("/virtualFolderSize/:branche,:idVirtualFolder")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(folderController.getVirtualFolderSize);

router
  .route("/isFolderVirtual/:branch,:idFolder")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(folderController.getIsFolderVirtual);

router
  .route("/foldersFromCodeValue/:branche,:code,:value")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(folderController.getFoldersFromCodeValue);

router
  .route("/folderName/:branche,:idFolder")
  .all(function (req, res, next) {
    if (req.method !== "PATCH") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .patch(folderController.patchFolderName);

/**
 *  __  __      _            _       _
 * |  \/  | ___| |_ __ _  __| | __ _| |_ __ _
 * | |\/| |/ _ \ __/ _` |/ _` |/ _` | __/ _` |
 * | |  | |  __/ || (_| | (_| | (_| | || (_| |
 * |_|  |_|\___|\__\__,_|\__,_|\__,_|\__\__,_|
 */
router
  .route("/metadatavalue/:item_id,:item_type,:model,:root,:lang")
  .all(function (req, res, next) {
    if (req.method !== "GET" && req.method !== "PUT" && req.method !== "PATCH" && req.method !== "DELETE") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(metadataController.getMetadataValue)
  .put(metadataController.createMetadataValue)
  .patch(metadataController.patchMetadataValue)
  .delete(metadataController.deleteMetadataValue);

router
  .route("/metadatamodel/:root,:lang")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req.res);
    } else {
      next();
    }
  })
  .get(metadataController.getMetadataModel);

router
  .route("/metadatamodelProject/:root,:idFolder,:lang")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req.res);
    } else {
      next();
    }
  })
  .get(metadataController.getMetadataModelProject);

router
  .route("/metadatamodelCorpus/:root,:idFolder,:lang")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req.res);
    } else {
      next();
    }
  })
  .get(metadataController.getMetadataModelCorpus);

router
  .route("/allmetadata/:id,:item_type,:root,:lang")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req.res);
    } else {
      next();
    }
  })
  .get(metadataController.getAllMetadata);

router
  .route("/metadata/:root,:model,:lang")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(metadataController.getMetadata);

router
  .route("/metadataModelType/:root,:model,:itemType,:lang")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(metadataController.getMetadataWithType);

router
  .route("/metadataList/:root")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(metadataController.getMetadataList);

router
  .route("/metadataListId/:root,:listId")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(metadataController.getMetadataListId);

router
  .route("/metadataIdFromCode/:root,:code")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(metadataController.getMetadataIdFromCode);

router
  .route("/metadataValueFromCode/:branche,:idItem,:code")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(metadataController.getMetadataValueFromCode);

router
  .route("/metadataMappingaLTAG3D/:root")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(metadataController.getMetadataMappingaLTAG3D);

router
  .route("/metadataJSON/:root,:model")
  .all(function (req, res, next) {
    if (req.method !== "GET" && req.method !== "PUT") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(jsonController.metadataFromModel)
  .put(jsonController.readMetadataValueFromJson);

router
  .route("/metadataLabels/:root,:idFolder,:lang")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(metadataController.getMetadataLabels);

router
  .route("/metadataLabelsFromCode/:root,:code,:lng")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(metadataController.getMetadataLabelsFromCode);

router
  .route("/itemsFromCodeValue/:branche,:code,:value")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(metadataController.getItemsFromCodeValue);

router
  .route("/itemsFromModelCodeValue/:branche,:model,:code,:value")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(metadataController.getItemsFromModelCodeValue);

/**
 *
 *      _ ____   ___  _   _
 *     | / ___| / _ \| \ | |
 *   _ | \___ \| | | |  \| |
 * | |_| |___) | |_| | |\  |
 *  \___/|____/ \___/|_| \_|
 *
 *
 */

router
  .route("/metadatavalueFromJson/:root,:itemId,:itemType,:model")
  .all(function (req, res, next) {
    if (req.method != "PUT") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .put(jsonController.createMetadataValueFromJson);

router
  .route("/metadatavalueFromXml")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(jsonController.readMetadatavalueFromXML);

router
  .route("/metadatavalueFromXmlSIP")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(jsonController.readMetadatavalueFromXMLSIP);

router
  .route("/ThesaurusPactolsFromJSON/:root,:thes")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(jsonController.readThesaurusPactolsFromJSON);

router
  .route("/Frantiq/:root,:thesaurus")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(jsonController.readThesaurusPactolsFranticFromJSON);

router
  .route("/FrantiqPoly/:root,:thesaurus")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(jsonController.readThesaurusPactolsFranticPolyFromJSON);

router
  .route("/OpenThesoMulti/:root,:thesaurus,:file")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(jsonController.readThesaurusMultiOpenThesoFromJSON);

router
    .route("/OpenthesoMultiNew/:root,:thesaurus,:file")
    .all(function (req, res, next) {
        if (req.method != "GET") {
            responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
        } else {
            next();
        }
    })
    .get(jsonController.readThesaurusOpenthesoMultiNew);

router
  .route("/RAZThesaurusMulti/:branche,:thesaurus")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(jsonController.RAZThesaurusMulti);

router
  .route("/OpenTheso2multi/:root,:thesaurus,:file")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(jsonController.readThesaurusMultiOpenTheso2FromJSON);

router
  .route("/PeriodO")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(jsonController.readFromJson);

router
  .route("/geonameThesMulti/:branche,:thesaurus,:geocode")
  .all(function (req, res, next) {
    if (req.method != "PUT" && req.method != "PATCH") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .put(jsonController.creategeothesitem)
  .patch(jsonController.updategeothesitem);

router
  .route("/geonameThesMultiXML/:branche,:thesaurus,:geocode")
  .all(function (req, res, next) {
    if (req.method != "PUT") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .put(jsonController.creategeothesitemXML);

router
  .route("/geonameThesMultiLocalJSON/:branche,:thesaurus,:geocode")
  .all(function (req, res, next) {
    if (req.method !== "PUT") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .put(jsonController.creategeothesitemLocalFileJSON);

router
  .route("/ingestJSON/:branch,:idRootFolder")
  .all(function (req, res, next) {
    if (req.method !== "GET" && req.method !== "PUT") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(jsonController.getJSONFiles)
  .put(jsonController.ingestJSON);

router
  .route("/ingestJSONpassport/:branch,:rootFolder,:idUser,:idObject,:idModel")
  .all(function (req, res, next) {
    if (req.method !== "PUT") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .put(jsonController.ingestJSONpassport);

/**
 *
  _____ _ _        ____            _
 |  ___(_) | ___  / ___| _   _ ___| |_ ___ _ __ ___
 | |_  | | |/ _ \ \___ \| | | / __| __/ _ \ '_ ` _ \
 |  _| | | |  __/  ___) | |_| \__ \ ||  __/ | | | | |
 |_|   |_|_|\___| |____/ \__, |___/\__\___|_| |_| |_|
                         |___/
 *
 */

router
  .route("/fsdirSynchFirst/:root")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(fsController.getfsdirsynchFirst);

router
  .route("/fsdirSynch/:root,:idFolder,:profondeur,:mode")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(fsController.getfsdirsynch);

router
  .route("/fsdirSynchNext/:branche,:idFolder,:profondeur,:mode")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(fsController.getfsdirsynchNext);

router
  .route("/fsdirSynchClean/:branche,:idFolder,:mode,:del,:recursive")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(fsController.getfsdirsynchClean);

router
  .route("/fsdirSynchInfoPath/:branche,:mode,:del")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(fsController.getfssynchInfoPath);

router
  .route("/fsdirSynchFin/:root,:idFolder")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(fsController.getfsdirsynchFinal);

router
  .route("/fsdirImages/:root,:idFolder")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(fsController.getfsdirGlobal);

router
  .route("/dbdir")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(fsController.getDbDirTab);

router
  .route("/dbPath/:root")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(fsController.getDbPath);

router
  .route("/dbPathFolder/:root,:idFolder")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(fsController.getDbPathDir);

router
  .route("/dbAllPathFolder/:root,:idFolder")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(fsController.getDbAllPathDir);

/**
 *
 * _____ _
 *|_   _| |__   ___  ___  __ _ _   _ _ __ _   _ ___
 *  | | | '_ \ / _ \/ __|/ _` | | | | '__| | | / __|
 *  | | | | | |  __/\__ \ (_| | |_| | |  | |_| \__ \
 *  |_| |_| |_|\___||___/\__,_|\__,_|_|   \__,_|___/
 *
 *
 */
router
  .route("/thesaurus/:root,:nameThesaurus")
  .all(function (req, res, next) {
    if (req.method != "GET" && req.method != "PUT") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(thesaurusController.getThesaurus)
  .put(thesaurusController.addThesaurusElement);

router
  .route("/thesaurusSimple/:root,:thesaurus")
  .all(function (req, res, next) {
    if (req.method !== "GET" && req.method !== "PUT") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  //.get(thesaurusController.getThesaurusSimple)
  .put(thesaurusController.addThesaurusSimple);

router
  .route("/thesaurusPactols/:root,:thesaurus")
  .all(function (req, res, next) {
    if (req.method !== "GET" && req.method !== "PUT") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(thesaurusController.getThesaurusPactols)
  .put(thesaurusController.addThesaurusPactols);

router
  .route("/thesaurusPactolsGeo/:root,:name")
  .all(function (req, res, next) {
    if (req.method !== "GET" && req.method !== "PUT") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(thesaurusController.getThesaurusPactolsGeo)
  .put(thesaurusController.addThesaurusPactolsGeo);

router
  .route("/thesaurusMulti/:root,:thesaurus")
  .all(function (req, res, next) {
    if (req.method !== "GET" && req.method !== "PUT" && req.method !== "PATCH") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(thesaurusController.getThesaurusMulti)
  .put(thesaurusController.addThesaurusMulti)
  .patch(thesaurusController.updateThesaurusMulti);

router
    .route("/thesaurusTranslate/:root,:thesaurus,:type")
    .all(function (req, res, next) {
        if (req.method !== "PATCH") {
            responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
        } else {
            next();
        }
    })
    .patch(thesaurusController.updateThesaurusTranslation);

router
  .route("/thesaurusMultiChildren/:root,:thesaurus,:idParent")
  .all(function (req, res, next) {
    if (req.method !== "GET" && req.method !== "DELETE" && req.method !== "PATCH") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(thesaurusController.getThesaurusMultiChildren)
  .delete(thesaurusController.deleteThesaurusMultiChildren)
  .patch(thesaurusController.patchHasChildThesaurusMultiChildren);

router
  .route("/thesaurusMultiConceptItem/:root,:thesaurus,:idThes")
  .all(function (req, res, next) {
    if (req.method !== "GET" && req.method !== "PUT") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(thesaurusController.getThesaurusMultiConceptItem);

router
  .route("/thesaurusMultiByName/:root,:thesaurus")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(thesaurusController.getThesaurusMultiByName);

router
  .route("/thesaurusMultiByGlobalRank/:root,:thesaurus")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(thesaurusController.getThesaurusMultiByGlobalRank);

router
  .route("/thesaurusMultiByShortName/:root,:thesaurus")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(thesaurusController.getThesaurusMultiByShortName);

router
  .route("/thesaurusMultiUpdate/:root,:thesaurus")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(thesaurusController.getThesaurusMultiToUpdate);

router
  .route("/thesaurusMultiOrigin/:root,:idProject")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(thesaurusController.getThesaurusMultiOrigin);

router
  .route("/thesaurusSimpleOrigin/:root,:idProject")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(thesaurusController.getThesaurusSimpleOrigin);

router
  .route("/thesaurusPactolsOrigin/:root,:idProject")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(thesaurusController.getThesaurusPactolsOrigin);

router
  .route("/thesaurusTree/:root,:thes,:idThes")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(thesaurusController.getThesaurusTree);

router
  .route("/thesaurusTreeMulti/:root,:thes,:idThes")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(thesaurusController.getThesaurusTreeMulti);

router
    .route("/thesaurusTreeMultiLng/:root,:thes,:idThes,:lng")
    .all(function (req, res, next) {
        if (req.method != "GET") {
            responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
        } else {
            next();
        }
    })
    .get(thesaurusController.getThesaurusTreeMultiLng);


router
  .route("/thesaurusTreePactols/:root,:thes,:idThes")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(thesaurusController.getThesaurusTreePactols);

router
  .route("/thesaurusTreePactolsPoly/:root,:thes,:idThes")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(thesaurusController.getThesaurusTreePactolsPoly);

router
  .route("/thesaurusOrigin/:root")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(thesaurusController.getThesaurusOrigin);

router
  .route("/thesaurusOriginFolder/:root,:idFolder")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(thesaurusController.getThesaurusOriginFolder);

router
  .route("/explorethes/:root,:thes,:idThes")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(thesaurusController.getThesaurusItem);

router
  .route("/explorethesNB/:branche,:thes,:idThes")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(thesaurusController.exploreThesaurusItemNB);

router
  .route("/exploreThesaurusNB/:branch,:type,:thesaurus,:id_thes")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(thesaurusController.exploreThesaurusNB);

router
  .route("/explorethesNBGeneral/:branche,:idThes,:thesaurus")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(thesaurusController.exploreThesaurusItemNBGeneral);

router
  .route("/explorethesMulti/:root,:thes,:idThes")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(thesaurusController.exploreThesaurusMultiItem);

router
  .route("/explorethesMultiNB/:branche,:thes,:idThes")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(thesaurusController.exploreThesaurusMultiItemNB);

router
  .route("/explorethesPactols/:root,:thes,:idThes")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(thesaurusController.exploreThesaurusPactolsItem);

router
  .route("/explorethesPactolsNB/:branche,:thes,:idThes")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(thesaurusController.exploreThesaurusPactolsItemNB);

router
  .route("/explorethesPactolsGeoNB/:branche,:idThes")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(thesaurusController.exploreThesaurusPactolsGeoItemNB);

router
  .route("/explorethesMultiNBGeneral/:branche,:idThes,:thesaurus")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(thesaurusController.exploreThesaurusMultiItemNBGeneral);

router
  .route("/explorethesMultiPage/:branche,:thes,:idThes")
  .all(function (req, res, next) {
    if (req.method !== "POST") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .post(thesaurusController.exploreThesaurusMultiItemPage);

router
  .route("/explorethesPactolsPage/:branche,:thes,:idThes")
  .all(function (req, res, next) {
    if (req.method !== "POST") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .post(thesaurusController.exploreThesaurusPactolsItemPage);

router
  .route("/explorethesPactolsGeoPage/:branche,:idThes")
  .all(function (req, res, next) {
    if (req.method !== "POST") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .post(thesaurusController.exploreThesaurusPactolsGeoItemPage);

router
  .route("/explorethesPage/:branche,:thes,:idThes")
  .all(function (req, res, next) {
    if (req.method !== "POST") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .post(thesaurusController.exploreThesaurusItemPage);

router
  .route("/exploreConceptThes/:root,:name,:lang,:thestable")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(thesaurusController.getThesaurusItemFromName);

router
  .route("/exploreConceptThesTotal/:root,:idThes,:thestable")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(thesaurusController.getThesaurusNBItemFromIdthes);

router
  .route("/ThesaurusPactolsName/:root,:thesaurus,:name")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(thesaurusController.getThesaurusPactolsName);

router
  .route("/ThesaurusPactolsNameFilter/:root,:thesaurus,:filter,:name")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(thesaurusController.getThesaurusPactolsNameFilter);

router
  .route("/ThesaurusPactolsGeoName/:root,:name")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(thesaurusController.getThesaurusPactolsGeoName);

router
  .route("/ThesaurusPactolsItem/:root,:type,:itemId")
  .all(function (req, res, next) {
    if (req.method !== "GET" && req.method !== "DELETE") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(thesaurusController.getThesaurusPactolsItem)
  .delete(thesaurusController.deleteThesaurusPactolsItem);

router
  .route("/ThesaurusPactolsGeoItem/:root,:type,:itemId")
  .all(function (req, res, next) {
    if (req.method !== "GET" && req.method !== "DELETE") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(thesaurusController.getThesaurusPactolsGeoItem)
  .delete(thesaurusController.deleteThesaurusPactolsGeoItem);

router
  .route("/ThesaurusPactolsConcept/:root,:conceptId")
  .all(function (req, res, next) {
    if (req.method != "GET" && req.method != "DELETE") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(thesaurusController.getThesaurusPactolsConcept);

router
  .route("/ThesaurusMultiConcept/:root,:thesaurus,:conceptId")
  .all(function (req, res, next) {
    if (req.method !== "GET" && req.method !== "DELETE") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(thesaurusController.getThesaurusMultiConcept)
  .delete(thesaurusController.deleteThesaurusMultiConcept);

router
  .route("/ThesaurusMaisonItem/:root,:type,:itemId")
  .all(function (req, res, next) {
    if (req.method !== "GET" && req.method !== "DELETE") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(thesaurusController.getThesaurusMaisonItem)
  .delete(thesaurusController.deleteThesaurusMaisonItem);

router
    .route("/ThesaurusItemQualifier/:root,:type,:itemId")
    .all(function (req, res, next) {
        if (req.method != "DELETE") {
            responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
        } else {
            next();
        }
    })
    .delete(thesaurusController.deleteThesaurusItemQual);

router
  .route("/ThesaurusMultiItem/:root,:type,:itemId")
  .all(function (req, res, next) {
    if (req.method != "GET" && req.method != "DELETE") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(thesaurusController.getThesaurusMultiItem)
  .delete(thesaurusController.deleteThesaurusMultiItem);

router
    .route("/ThesaurusMultiItemQualifier/:root,:type,:itemId")
    .all(function (req, res, next) {
        if (req.method != "DELETE") {
            responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
        } else {
            next();
        }
    })
    .delete(thesaurusController.deleteThesaurusMultiItemQual);


router
  .route("/ThesaurusMultiName/:root,:thesaurus,:name")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(thesaurusController.getThesaurusMultiName);

router
  .route("/ThesaurusMultiNameUser/:root,:thesaurus,:name,:idUser")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(thesaurusController.getThesaurusMultiNameUser);

router
  .route("/ThesaurusMultiNameGeneral/:root,:name")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(thesaurusController.getThesaurusMultiNameGeneral);

router
  .route("/ThesaurusGeo/:name")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(thesaurusController.getThesaurusGeo);

router
  .route("/ThesaurusName/:root,:thesaurus,:name")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(thesaurusController.getThesaurusName);

router
  .route("/ThesaurusMultiFromName/:root,:name")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(thesaurusController.getThesaurusMultiFromName);

router
  .route("/thesaurusPeriodO/:root")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(thesaurusController.getThesaurusPeriodO);

router
  .route("/thesaurusPeriodoItem/:root,:type,:itemId")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(thesaurusController.getThesaurusPeriodOItem);

router
  .route("/thesaurusTreePeriodo/:branch,:lang")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(thesaurusController.getThesaurusTreePeriodo);

router
  .route("/allThesaurusName/:branch,:lang")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(thesaurusController.getAllThesaurusName);

router
  .route("/allThesaurusNameProject/:branch,:idFolder,:lang")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(thesaurusController.getAllThesaurusNameForProject);

router
  .route("/orderThesaurus/:branche,:thesaurus")
  .all(function (req, res, next) {
    if (req.method != "GET" && req.method != "PATCH") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(thesaurusController.getOrderThesaurus)
  .patch(thesaurusController.patchThesOrder);

router
  .route("/thesaurusProject/:branche,:idProject,:lng")
  .all(function (req, res, next) {
    if (req.method !== "GET" && req.method !== "PUT") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(thesaurusController.getAllThesaurusForProject)
  .put(thesaurusController.addThesaurusPactolsForProject);

router
  .route("/ThesaurusSimpleName/:branch,:thesaurus,:lng,:name")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(thesaurusController.getThesaurusSimpleName);

router
  .route("/ThesaurusPeriodoName/:branch,:lang,:name")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(thesaurusController.getThesaurusPeriodoName);

router
  .route("/ThesaurusPactolsNameSearch/:branch,:name")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(thesaurusController.getThesaurusPactolsNameSearch);

router
  .route("/thesaurusSearchNames/:branch,:search")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(thesaurusController.getThesaurusSearchNames);

router
  .route("/SitesFromGeonames/:branch")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(thesaurusController.getSitesFromGeonames);

router
  .route("/SiteFromGeonames/:branch,:codeGeonames")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(thesaurusController.getSiteFromGeonames);

router
  .route("/ThesItemFromMultiCodeThesaurus/:branch,:codeIdThes,:thesaurus")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(thesaurusController.getThesItemFromMultiCodeThesaurus);

router
  .route("/ThesItemFromPactolsCodeThesaurus/:branch,:codeIdThes,:thesaurus")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(thesaurusController.getThesItemFromPactolsCodeThesaurus);

router
  .route("/ThesItemFromPactolsGeoCodeThesaurus/:branch,:codeIdThes")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(thesaurusController.getThesItemFromPactolsGeoCodeThesaurus);

router
  .route("/ThesItemFromSimpleCodeThesaurus/:branch,:codeIdThes,:thesaurus")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(thesaurusController.getThesItemFromSimpleCodeThesaurus);

/**
 * _____
 *|_   _|_ _  __ _
 *  | |/ _` |/ _` |
 *  | | (_| | (_| |
 *  |_|\__,_|\__, |
 *           |___/
 *
 *
 */
router
  .route("/tag/:root,:type,:itemId")
  .all(function (req, res, next) {
    if (req.method !== "GET" && req.method !== "DELETE" && req.method !== "PATCH") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(tagController.getTagItem)
  .delete(tagController.deleteTagItem)
  .patch(tagController.treatTagItem);

router
  .route("/tags/:branche,:itemType,:thesaurusType")
  .all(function (req, res, next) {
    if (req.method !== "PATCH") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .patch(tagController.treatTagsItem);

/**
 *
 *  ____
 * / ___|_ __ ___  _   _ _ __
 *| |  _| '__/ _ \| | | | '_ \
 *| |_| | | | (_) | |_| | |_) |
 * \____|_|  \___/ \__,_| .__/
 *                      |_|
 **
 */
router
  .route("/group")
  .all(function (req, res, next) {
    if (req.method != "GET" && req.method != "PUT" && req.method != "DELETE") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(groupController.getGroup)
  .put(groupController.createGroup)
  .delete(groupController.deleteGroup);

router
  .route("/groupAccess/:root,:groupId")
  .all(function (req, res, next) {
    if (req.method != "GET" && req.method != "PUT" && req.method != "DELETE") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(groupController.getGroupAccess)
  .put(groupController.createGroupAccess)
  .delete(groupController.deleteGroupAccess);

router
  .route("/groupAccessWrite/:root,:groupId")
  .all(function (req, res, next) {
    if (req.method != "GET" && req.method != "PUT" && req.method != "DELETE") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(groupController.getGroupAccessWrite)
  .put(groupController.createGroupAccessWrite)
  .delete(groupController.deleteGroupAccessWrite);

router
  .route("/groupAccessAdmin/:root,:groupId")
  .all(function (req, res, next) {
    if (req.method != "GET" && req.method != "PUT" && req.method != "DELETE") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(groupController.getGroupAccessAdmin)
  .put(groupController.createGroupAccessAdmin)
  .delete(groupController.deleteGroupAccessAdmin);

router
  .route("/groupWriteAccessAdmin/:root,:groupId")
  .all(function (req, res, next) {
    if (req.method != "GET" && req.method != "PUT" && req.method != "DELETE") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(groupController.getGroupWriteAccessAdmin)
  .put(groupController.createGroupWriteAccessAdmin)
  .delete(groupController.deleteGroupWriteAccessAdmin);

router
  .route("/groupUser/:userId")
  .all(function (req, res, next) {
    if (req.method != "GET" && req.method != "PUT" && req.method != "DELETE") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(groupController.getGroupUser)
  .put(groupController.createGroupUser)
  .delete(groupController.deleteGroupUser);

router
  .route("/groupUsers/:groupId")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(groupController.getGroupUsers);

router
  .route("/groupOnlyForbiddenfolders/:groupId,:root")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(groupController.getGroupOnlyForbiddenFolders);

/**
 *   ____                      _
 * /  ___|  ___  __ _ _ __ ___| |__
 * \___ \ / _ \/ _` | '__/ __| '_ \
 *  ___) |  __/ (_| | | | (__| | | |
 * |____/ \___|\__,_|_|  \___|_| |_|
 *
 *
 *
 */
router
  .route("/searchNb/:branch,:idFolder")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(searchController.searchNb);

router
  .route("/search/:branch,:idFolder")
  .all(function (req, res, next) {
    if (req.method != "POST") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .post(searchController.search);

router
  .route("/saveSearch/:branch,:projectId,:userId,:name,:search")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(searchController.saveSearch);

router
  .route("/getUserSavedSearches/:branch,:userId/:projectId?")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(searchController.getUserSavedSearches);

router
  .route("/removeUserSavedSearch/:branch,:userId/:searchId?")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(searchController.removeUserSavedSearch);

/**
 *  ____                                     _
   / ___|___  _ __ ___  _ __ ___   ___ _ __ | |_
  | |   / _ \| '_ ` _ \| '_ ` _ \ / _ \ '_ \| __|
  | |__| (_) | | | | | | | | | | |  __/ | | | |_
   \____\___/|_| |_| |_|_| |_| |_|\___|_| |_|\__|
 *
 */
router
  .route("/comment/:branche,:idMainFolder,:userId")
  .all(function (req, res, next) {
    if (req.method != "GET" && req.method != "PUT" && req.method != "DELETE" && req.method != "PATCH") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(commentController.getComment)
  .put(commentController.createComment)
  .delete(commentController.deleteComment)
  .patch(commentController.patchComment);

router
  .route("/exploreCommentedItem/:branche,:idMainFolder")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(commentController.getCommentedItem);

/**
 * __  ____  __ _
   \ \/ /  \/  | |
    \  /| |\/| | |
    /  \| |  | | |___
   /_/\_\_|  |_|_____|

 */
router
  .route("/xmlGenerateDOI/:type,:idFolder")
  .all(function (req, res, next) {
    if (req.method !== "PUT") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .put(xmlController.createXMLForCND3DDOI);

router
  .route("/geonameNearby/:idFolder")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(xmlController.getNearbyPlace);

router
  .route("/carare/:idItem")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(xmlController.readCarareFile);

router
  .route("/xmlGenerateEDM/:branch,:idItem,:itemType")
  .all(function (req, res, next) {
    if (req.method !== "POST") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .post(xmlController.createXMLForCND3DEDM);

/**
 *  ____   ___ ___
 * |  _ \ / _ \_ _|
 * | | | | | | | |
 * | |_| | |_| | |
 * |____/ \___/___|
 *
 */
router
  .route("/generateDOI/:branche,:idItem,:itemType")
  .all(function (req, res, next) {
    if (req.method != "GET" && req.method != "PUT") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(doiController.getDOI)
  .put(doiController.createDOI);

router
  .route("/itemFromDOI/:branche,:idDOI")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(doiController.getItemFromDOI);

router
  .route("/creatorForDOI/:branche,:type,:idItem")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(doiController.getCreatorForDOI);

router
  .route("/dateCreationForDOI/:branche,:type,:idItem")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(doiController.getCreationaDateForDOI);

router
  .route("/contribForDOI/:branche,:type,:idItem")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(doiController.getContribForDOI);

router
  .route("/manageDOI/:branche,:idDoi")
  .all(function (req, res, next) {
    if (req.method !== "PATCH") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  //.get(doiController.getDOI)
  .patch(doiController.patchFolderDOI);

router
  .route("/validateDOI/:branche,:type,:idItem")
  .all(function (req, res, next) {
    if (req.method !== "GET" && req.method !== "PATCH") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(doiController.getCompleteDOI)
  .patch(doiController.patchDOI);

router
  .route("/validateAllDOIs/:branche")
  .all(function (req, res, next) {
    if (req.method !== "PATCH") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .patch(doiController.patchAllDOIs);

router
  .route("/listNonValidatedDois/:branche,:column,:order")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(doiController.getNonValidatedDOIs);

router
  .route("/listValidatedDois/:branche,:column,:order")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(doiController.getValidatedDOIs);

router
  .route("/DOIListId/:idFolder")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(doiController.getListId);

router
  .route("/jsonInfoForDOI/:branche,:type,:idItem")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(doiController.getJsonInfoItem);

router
  .route("/confirmCreationDOI/:branche,:type,:idItem")
  .all(function (req, res, next) {
    if (req.method !== "PATCH") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .patch(doiController.confirmCreationDOI);

router
  .route("/DOIs/:branche,:column,:order")
  .all(function (req, res, next) {
    if (req.method !== "GET" && req.method !== "PATCH") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(doiController.getDOIs)
  .patch(doiController.patchUniqueDOI);

router
  .route("/DOIbyName/:branche,:name")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(doiController.getDOIbyName);

/**
 *
 *   ____ ______     __
 *  / ___/ ___\ \   / /
 * | |   \___ \\ \ / /
 * | |___ ___) |\ V /
 *  \____|____/  \_/
 *
 */
router
  .route("/MetadataFromCSV/:branche,:model,:root_dir")
  .all(function (req, res, next) {
    if (req.method !== "GET" && req.method !== "PUT") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(csvController.getCsvInfo)
  .put(csvController.createMetadataFromCsv);

router
  .route("/MetadataFromCSVObject/:branche,:model,:root_dir")
  .all(function (req, res, next) {
    if (req.method !== "PUT") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .put(csvController.createMetadataFromCsvObject);

/**
 *  _____     _                          _ _
 * |___ /  __| |_ __ ___ _ __   ___  ___(_) |_ ___  _ __ _   _
     |_ \ / _` | '__/ _ \ '_ \ / _ \/ __| | __/ _ \| '__| | | |
    _ _) | (_| | | |  __/ |_) | (_) \__ \ | || (_) | |  | |_| |
   |____/ \__,_|_|  \___| .__/ \___/|___/_|\__\___/|_|   \__, |
                        |_|                              |___/
 *
 */
router
  .route("/3drepository/:idFolder,:lang")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(repositoryController.getDeposit);

/**
 *
 *    ____
 *   / ___|___  _ __ _ __  _   _ ___
 *  | |   / _ \| '__| '_ \| | | / __|
 *  | |___ (_) | |  | |_) | |_| \__ \
 *   \____\___/|_|  | .__/ \__,_|___/
 *                  |_|
 *
 *
 */

router
  .route("/Corpus")
  .all(function (req, res, next) {
    if (req.method !== "GET" && req.method !== "PUT") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method is not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(corpusController.getCorpuss)
  .put(corpusController.createCorpus);

router
  .route("/corpusCard")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(corpusController.getCorpusCard);

router
  .route("/corpusMap")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(corpusController.getCorpusMap);

router
  .route("/corpusFull/:branche,:idCorpus,:lang")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(corpusController.getCorpusFull);

router
  .route("/CorpusRoot/:root")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(corpusController.getCorpusRootList);

router
  .route("/corpusName/:idCorpus")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(corpusController.getCorpusName);

router
  .route("/getCorpusId/:idFile")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(corpusController.getCorpusId);

//    ____          _____
//   / __ \   /\   |_   _|
//  | |  | | /  \    | |
//  | |  | |/ /\ \   | |
//  | |__| / ____ \ _| |_
//   \____/_/    \_\_____|

router
  .route("/oai/:branch")
  .all(function (req, res, next) {
    if (req.method !== "GET" && req.method !== "POST") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(oaiController.oai)
  .post(oaiController.oai);

/**
 *   _             _                           _     _
    / \   _ __ ___| |__   ___  ___   __ _ _ __(_) __| |
   / _ \ | '__/ __| '_ \ / _ \/ _ \ / _` | '__| |/ _` |
  / ___ \| | | (__| | | |  __/ (_) | (_| | |  | | (_| |
 /_/   \_\_|  \___|_| |_|\___|\___/ \__, |_|  |_|\__,_|
                                    |___/
 *
 */
router
  .route("/branche/:lng?")
  .all(function (req, res, next) {
    if (req.method != "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(archeogridController.getBranche);

router
  .route("/licenseItem/:branche,:idItem,:itemType,:lng")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(archeogridController.getLicenseFromItem);

router
  .route("/license/:branche,:lng")
  .all(function (req, res, next) {
    if (req.method !== "GET") {
      responseHelper.sendError(501, "not_implemented", `${req.method} method not yet implemented`, req, res);
    } else {
      next();
    }
  })
  .get(archeogridController.getLicenseGeneralInfo);

router.route("/object/first-metadata/:branch/:item_id/:item_type").get(objectController.getFirstMetadata);

export default router;
