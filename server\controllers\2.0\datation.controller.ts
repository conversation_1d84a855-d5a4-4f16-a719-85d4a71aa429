import type { IDatabase } from "pg-promise";
import { z } from "zod";

import { defined } from "~/helpers/tools";
import { t } from "~/trpcContext";
import type { Branch } from "~/types/api";
import { branch, id, language } from "~/types/schemas";
import {
  datation,
  getDatationMetadataLinkOutput
} from "~/types/schemas/datation";

const mdacst3DnameToDBname: { [key: string]: string } = {
    "date_minDate": 'date_min',
    "date_maxDate": 'date_max',
    "date_literalDatation": 'date_literal',
    "date_periodo": 'id_periodo'
}

export default t.router({
    /**
     * Create a datation   
     * @param branch The affected branch
     * @param datation All the fields of an datation
     * @returns The id of the existing datation or the one created
     */
    createDatation: t.procedure
    .input(z.object({branch: z.string(), datation: datation}))
    .output(z.object({id: z.number().nullable()}))
    .mutation(async ({input, ctx}) => {
        const date_maxEqualQuery = input.datation.date_maxDate ? "date_max='" + input.datation.date_maxDate + "'" : "date_max is null";
        const id_periodoEqualQuery = input.datation.date_periodo ? "id_periodo='" + input.datation.date_periodo + "'" : "id_periodo is null";

        const datationExist = await ctx.database.oneOrNone(`
            SELECT id 
            FROM ${input.branch}_datation 
            WHERE date_min='${input.datation.date_minDate}' 
            AND ${date_maxEqualQuery} 
            AND date_literal ${input.datation.date_literalDatation === "" ? 'IS NULL' : `= '${input.datation.date_literalDatation}'`} 
            AND ${id_periodoEqualQuery}
            LIMIT 1`
        );
        
        if(datationExist){
            return z.object({id: z.number().nullable()}).parse(datationExist);
        }

        let field_query = [];
        let value_query = [];
        for(let [key, value] of Object.entries(input.datation)) {
            if(value !== ''){
                field_query.push(mdacst3DnameToDBname[key] || key);
                value_query.push(Number.isInteger(value) ? `${value}` : `'${value}'` );
            } 
        }
        field_query.push('date_min_formated');
        const mi = ctx.datation.formatDate(input.datation.date_minDate, 'min')
        value_query.push(`'${mi}'`);
        //value_query.push(ctx.datation.formatDate(input.datation.date_minDate, 'min'));
        field_query.push('date_max_formated');
        const ma = ctx.datation.formatDate(input.datation.date_maxDate ?? input.datation.date_minDate, 'max')
        value_query.push(`'${ma}'`);
        //value_query.push(ctx.datation.formatDate(input.datation.date_maxDate ?? input.datation.date_minDate, 'max'));

        console.log(`INSERT INTO ${input.branch}_datation (${field_query.join(',')}) VALUES (${value_query.join(',')}) RETURNING id`)

        let datation = await ctx.database.one(
            `INSERT INTO ${input.branch}_datation (${field_query.join(',')}) VALUES (${value_query.join(',')}) RETURNING id`
        );
        return z.object({id: z.number().nullable()}).parse(datation);
    }), 

    /**
     * Get all id_datation and linked id_metadata for a specific item  
     * @param id_item The item to search the datation and linked metadata from
     * @param item_type The type of the item
     * @param branch The affected branch
     * @returns The list datation and linked metadata ids
     */
    getDatationMetadataLink: t.procedure
    .input(z.object({id_item: z.number(), item_type: z.string(), branch: z.string()}))
    .output(getDatationMetadataLinkOutput)
    .query(async  ({input, ctx}) => {
        const data = await ctx.database.manyOrNone(
        `SELECT id_datation, id_metadata
        FROM ${input.branch}_datation_item
        WHERE id_item=${input.id_item}
        AND item_type='${input.item_type}'`
        );

        return getDatationMetadataLinkOutput.parse(data) ?? [];
    }),

    createDatationMetadataLink: t.procedure
    .input(z.object({id_item: z.number(), item_type: z.string(), id_datation: z.number(), id_metadata: z.number(), branch: z.string()}))
    .mutation(async ({input, ctx}) => {
        const exists = await ctx.database.oneOrNone(`
            SELECT * FROM ${input.branch}_datation_item
            WHERE id_item=$1 
            AND item_type=$2
            AND id_datation=$3 
            AND id_metadata=$4
            LIMIT 1`, [input.id_item, input.item_type, input.id_datation, input.id_metadata])
        if(exists){
            return true;
        }
        const result = await ctx.database.none(
            `INSERT INTO ${input.branch}_datation_item (id_item, item_type, id_datation, id_metadata) VALUES ($1,$2,$3,$4)`, [input.id_item, input.item_type, input.id_datation, input.id_metadata]
        );
        return true;
    }), 

    /**
     * Delete an datation--metadata link associated to an item
     * If after deletion the datation is not linked to any metadata/item, it's deleted
     * @param id_item The id of the item
     * @param item_type The type of the item
     * @param id_datation The id of the datation
     * @param id_metadata The id of the metadata
     * @param branch The affected branch
     * */
    deleteDatationMetadataLink: t.procedure
    .input(z.object({id_item: z.number(), item_type: z.string(), id_datation: z.number(), id_metadata: z.number(), branch: z.string()}))
    .mutation(async ({input, ctx}) => {
        await ctx.database.none(
            `DELETE FROM ${input.branch}_datation_item 
             WHERE id_item=$1 
             AND item_type=$2
             AND id_datation=$3 
             AND id_metadata=$4`, [input.id_item, input.item_type, input.id_datation, input.id_metadata]
        );

        // delete datation if it is not linked to any metadata/folder
        const used = await ctx.database.oneOrNone(
            `SELECT COUNT(id_item) as nb_link 
             FROM ${input.branch}_datation_item 
             WHERE id_datation=${input.id_datation}`
        )

        if(used.nb_link == 0){
            await ctx.database.none(
                `DELETE FROM ${input.branch}_datation WHERE id=${input.id_datation}`
            )
        }
    }),

    getDatationWithMetadataAndItem: t.procedure
    .input(z.object({id_item: z.number(), item_type: z.string(), id_metadata: z.number(), branch: z.string()}))
    .output(datation.array())
    .query(async ({input, ctx}) => {
        const result = await ctx.database.manyOrNone(
            `SELECT date_min, date_max, date_literal, id_periodo 
             FROM ${input.branch}_datation_item di 
             INNER JOIN ${input.branch}_datation d on di.id_datation = d.id 
             WHERE di.id_item=${input.id_item}
             AND di.item_type='${input.item_type}' 
             AND di.id_metadata=${input.id_metadata}`
        );

        let format_result: any = []
        result.forEach(datation => {
            let d = {
                date_minDate: datation.date_min,
                date_maxDate: datation.date_max,
                date_literalDatation: datation.date_literal,
                date_periodo: datation.id_periodo
            }
            format_result.push(d)
        })

        return datation.array().parse(format_result);
    }),

    linkDatationToPeriodoWithId: t.procedure
    .input(z.object({
        branch, 
        id_item: z.number(), 
        item_type: z.string(), 
        id_thes_periodo: z.string(), 
        public: z.boolean(), 
        id_user: z.number(),
        id_metadata: z.number().optional()
    }))
    .mutation(async ({input, ctx}) => {
        const qualifier = input.id_metadata ? (await ctx.database.oneOrNone(`SELECT code FROM ${input.branch}_metadata WHERE id=${input.id_metadata}`)).code || null : null;

        const exists = await ctx.database.oneOrNone(
            `SELECT * FROM ${input.branch}_thesaurus_periodo_item
            WHERE id_item=$1 
            AND item_type=$2
            AND id_thes_periodo=$3
            AND qualifier ${qualifier ? "= '"+qualifier+"'" : 'IS NULL'}`
            , [input.id_item, input.item_type, input.id_thes_periodo]
        )

        if(exists) return;

        const periodO = await ctx.database.oneOrNone(`
            SELECT * FROM ${input.branch}_thesaurus_periodo
            WHERE id_periodo=$1`, [input.id_thes_periodo]
        )

        if(!periodO) return;

        await ctx.database.none(
            `INSERT INTO ${input.branch}_thesaurus_periodo_item (id_periodo, id_thes_periodo, id_item, item_type, language, public, id_user, qualifier) 
            VALUES ($1,$2,$3,$4,$5,$6,$7,$8)`, [periodO.id, periodO.id_periodo, input.id_item, input.item_type, periodO.language, input.public, input.id_user, qualifier]
        );
    }),

    linkDatationToPeriodoWithoutId: t.procedure
    .input(z.object({
        branch, 
        id_item: z.number(), 
        item_type: z.string(),
        start_date: z.string(),
        end_date: z.string(), 
        public: z.boolean(), 
        id_user: z.number(),
        id_metadata: z.number().optional()}))
    .mutation(async ({input, ctx}) => {
        const qualifier = input.id_metadata ? (await ctx.database.oneOrNone(`SELECT code FROM ${input.branch}_metadata WHERE id=${input.id_metadata}`)).code || null : null;

        const start_date_split = input.start_date.split('-');
        const start_date_year = input.start_date[0] == '-' ? parseInt('-' + start_date_split[1]) : parseInt(start_date_split[0]);
        console.log("split 0 => ", start_date_split[0]);
        
        const end_date_split = input.end_date.split('-');
        const end_date_year = input.end_date[0] == '-' ? parseInt('-' + end_date_split[1]) : parseInt(end_date_split[0]); 

        const periodO = await ctx.database.manyOrNone(`
            SELECT * FROM ${input.branch}_thesaurus_periodo
            WHERE start_date::int <= ${start_date_year}
            AND stop_date::int >= ${end_date_year}`);

        // No periodO found matching the start_date and end_date
        if(!periodO || periodO.length == 0){
            return;
        }

        // Only one periodO found
        if(periodO.length == 1){
            console.log("ONE PERIODO FOUND => ", periodO[0].label);
            const exists = await ctx.database.oneOrNone(`
                SELECT * FROM ${input.branch}_thesaurus_periodo_item
                WHERE id_item=$1 
                AND item_type=$2
                AND id_thes_periodo=$3 
                AND qualifier ${qualifier ? "= '"+qualifier+"'" : 'IS NULL'}`, [input.id_item, input.item_type, periodO[0].id_periodo]
            )

            if(exists) return;

            await ctx.database.none(`
                INSERT INTO ${input.branch}_thesaurus_periodo_item (id_periodo, id_thes_periodo, id_item, item_type, language, public, user_id, qualifier)
                VALUES (${periodO[0].id}, '${periodO[0].id_periodo}', ${input.id_item}, '${input.item_type}', '${periodO[0].language}', ${input.public}, $1, $2)`, [input.id_user, qualifier]
            );
        }else{
            // Multiple periodO found
            const periodO_sorted = periodO.sort((a, b) => (b.end_date - b.start_date) - (a.end_date - a.start_date)); 
            let best_periodO = periodO_sorted.find(p => p.spatial_coverage_desc === 'France'); // Try to find a french periodO
            if(!best_periodO) {// No french periodO found -> pick the first one
                best_periodO = periodO_sorted[0];
            }

            const exists = await ctx.database.oneOrNone(`
                SELECT * FROM ${input.branch}_thesaurus_periodo_item
                WHERE id_item=$1 
                AND item_type=$2
                AND id_thes_periodo=$3 
                AND qualifier=$4`, [input.id_item, input.item_type, best_periodO.id_periodo, qualifier]
            )

            if(exists) return;

            await ctx.database.none(`
                INSERT INTO ${input.branch}_thesaurus_periodo_item (id_periodo, id_thes_periodo, id_item, item_type, language, public, id_user, qualifier)
                VALUES (${best_periodO.id}, '${best_periodO.id_periodo}', ${input.id_item}, '${input.item_type}', '${best_periodO.language}', ${input.public}, $1, $2)`, [input.id_user, qualifier]
            );
        }   
    }),

})  
  