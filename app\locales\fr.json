{"admin": {"category": {"folder": "Dossiers", "file": "Fichiers", "user": "Utilisateurs", "group": "Groupes", "metadata": "Métadonnées", "thesaurus": "Thesaurus", "organization": "Agent / Acteur & Organisation"}, "folder": {"manage": "Gestion des dossiers (ordre, édition)", "order": "Ordre arborescence des dossiers", "download": "Téléchargement d'un dossier", "maintenance": "Mise en maintenance d'un dossier", "permissions": "Statut public/privé", "public": "Dossiers publics", "private": "Dossiers privés"}, "file": {"sync": "Synchronisation de fichiers", "json": "Import de fichier json", "syncIPTC_EXIF": "Ajouter métadonnées IPTC/EXIF"}, "user": {"addTemp": "Nouveaux utilisateurs dépôt", "list": "Liste des utilisateurs", "add": "C<PERSON>er un utilisateur", "edit": "Modifier un utilisateur", "permissions": "Permissions d'un utilisateur", "assign": "Attribution des projets pour scribe", "entity": "Gestion des entités", "deleteTemp": "Suppression (temporaire) d'un utilisateur", "history": "Historique des modifications"}, "group": {"list": "Liste des groupes", "add": "Créer un groupe", "edit": "Modifier un groupe", "permissions": "Permissions d'un groupe"}, "metadata": {"csv": "Création de métadonnées/tables via CSV", "tags": "Ingérer des tags", "addModel": "<PERSON><PERSON>er un modèle de métadonnées", "editModel": "Éditer un modèle de métadonnées"}, "thesaurus": {"order": "Changer l'ordre dans le thesaurus", "add": "Ajouter un élément de thesaurus", "link": "Lier thesaurus et projets"}, "organization": {"sort": "Organisations - Voir, modifier, supprimer", "add": "Ajouter une organisation"}, "actor": {"sort": "Acteurs - <PERSON><PERSON>, modifier, supprimer", "add": "Ajouter un acteur"}, "content": {"BranchSelection": "Branche concernée", "ProjectSelection": "Projet concerné", "SearchProject": "Rechercher un projet", "folder": {"manage": {"submit": "Valider l'ordre", "branchFormTitle": "Gestion des dossiers pour quelle branche d'Archeogrid ?", "edit": {"displayFile": "Choisir une image pour représenter le projet :"}}, "order": {"projectSelection": "Choisir un projet pour l'organisation de l'arborescence", "submit": "Validation par niveau", "depth": "<PERSON><PERSON><PERSON>"}, "download": {"title": "De quel environnement voulez-vous voir les autorisations de téléchargement sur les dossiers ?", "downloadable": "Dossiers téléchargeables", "notDownloadable": "Dossiers non téléchargeables"}, "permissions": {"makePublic": "Rendre public", "makePublicExplanation": "Si un dossier est déclaré public, alors tous ses dossiers parents sont rendus publics", "makePrivate": "<PERSON><PERSON> privé", "makePrivateExplanation": "Si un dossier est déclaré privé, tous ses sous-dossiers sont rendus privés", "noneAssigned": "Aucun projet attribué"}}, "file": {"sync": {"title": "Synchronisation de fichiers dans la base de données", "rules": "Les dossiers suivants ne seront pas synchronisés :", "rule1": "Dossiers dont les noms commencent par '_',  par exemple : '_images'", "rule2": "Dossiers Archivage3D, créés par défaut pour tout projet", "chooseFolder": "Choisir un dossier", "modes": "Mode de synchronisation :", "mode1": "Dossiers seulement", "mode2": "Fichiers seulement", "mode3": "Dossiers et fichiers", "delete": "Synchronisation avec suppression des références de fichiers en base de données qui n'existent plus sur disque", "recursion": "Activer la récursion ?", "time1": "Synchronisation en", "time2": "étape(s)", "timeInfo": "<PERSON><PERSON> avez exécuté", "summaryStep1": "Synchronisation en", "summaryStep2": "étape(s)", "summaryDelete": "Suppression activée ?"}}, "group": {"edit": {"form": "Quel groupe ?"}}, "metadata": {"addModel": {"form": {"type": "Type de données affectées", "name": "Nom du nouveau modèle", "visible": "Visibilité", "now": "Maintenant", "later": "Plus tard", "projects": "Quels projets affectés ?", "all": "Tous les projets", "select": "Sélection de projets", "desc": "Description du modèle"}}, "selectModel": "<PERSON><PERSON><PERSON><PERSON>", "confirmDeleteTitle": "Supp<PERSON><PERSON> le modèle", "confirmDelete": "Voulez-vous vraiment supprimer ce modèle de métadonnées ?"}, "csvMetadata": {"CreateTable": "<PERSON><PERSON><PERSON>", "UpdateTable": "Modifier la table", "CreateModel": "<PERSON><PERSON><PERSON> le modèle", "UpdateModel": "Modifier le modèle", "IngestCSV": "Ingérer le CSV", "TableContent": "Contenu de la table", "ModelContent": "Contenu du modèle", "ListOfCSV": "Liste des CSV disponibles pour", "ModelItemType": "Type d'items du modèle", "TableItemType": "Type d'items de la table", "LastTableUpdate": "Dernière ingestion dans la table", "LastModelUpdate": "Dernière ingestion dans le modèle", "TableName": "Nom de la table", "ModelName": "Nom du modèle", "Menu": "Menu CSV metadata", "MetadataCSVTitle": "Création de métadonnées via CSV", "MetadataCSVDescription": "Fonctionnalité permettant d'introduire au sein d'ArcheoGRID des données à partir de fichiers CSV.", "searchCsvMetadataLabel": "Recherche par nom de projet ou de fichier CSV", "csvMetadataTableOverview": "Précédentes ingestions", "moreOptionButton": "Plus d'options", "ingest": {"IngestCSVTitle": "Ingérer un CSV", "IngestCSVDescription": "Ingérer un fichier CSV, dans une table et/ou un modèle de métadonnées. La mise à jour du modèle nécessite, au préalable, l'ingestion du CSV dans la table.", "InfoSelectRow": "Sélectionner une ligne pour accéder aux actions", "InfoSelectRowCreateTable": "Créer la table avant de pouvoir ingérer le CSV", "InfoSelectRowCreateModel": "<PERSON><PERSON><PERSON> le modèle avant de pouvoir ingérer le CSV", "InfoSelectRowIngestAvailable": "Vous pouvez ingérer le CSV ou mettre à jour la table et/ou le modèle"}, "model": {"CreateModelTitle": "<PERSON><PERSON><PERSON> un modèle", "CreateModelDescription": "Créer un modèle de métadonnées à partir des colonnes d'un fichier CSV. Paramétrage des métadonnées (type, isunique, isrequired...) et du modèle.", "Code": "Code de la métadonnée", "Label": "Label de la métadonnée (table metadata_label)", "Description": "Description de la métadonnée (table metadata_label)", "Type": "Type de la métadonnée (status de la table metadata)", "IsUnique": "Indique si la métadonnée ne possède qu'une seule valeur (isunique de la table metadata)", "IsRequired": "Indique si la métadonnée est obligatoire (y de la table metadata)", "CreateModelEndTitle": "Création du modèle terminée", "CreateModelEndDescription": "Le modèle a bien été créé, vous pouvez maintenant ingérer le CSV dans le modèle ou si ce n'est pas encore le cas, créer la table correspondante."}, "table": {"CreateTableTitle": "<PERSON><PERSON><PERSON> une table", "CreateTableDescription": "C<PERSON>er une table en base de données à partir des colonnes d'un fichier CSV.", "ConfirmUpdate": "Voulez-vous vraiment mettre à jour la table", "AllDataWillBeLost": "Toutes les anciennes données de la table seront perdues", "CreateTableEndTitle": "Création de la table terminée", "CreateTableEndDescription": "La table a bien été créée, vous pouvez maintenant créer le modèle de métadonnées correspondant ou, si le modèle existe déjà, ingérer le CSV dans le modèle."}, "NoCSV": "Aucun CSV disponible", "UseTab": "Utiliser la tabulation", "DelimiterError": "Inscrivez un délimiteur, ou cliquez sur le bouton 'Utiliser la tabulation'", "TableNameError": "Inscrivez un nom de table", "StartQuickIngest": "<PERSON><PERSON><PERSON><PERSON> l'ingestion", "quick": {"QuickIngestTitle": "Ingestion CSV", "QuickIngestDescription": "Un guide simplifié, étape par étape, pour ingérer un CSV dans la base de données. Assurez-vous d'avoir placé votre CSV dans le dossier 'metadata' de votre projet, et que la première colonne contienne les noms des items.", "summary": {"table_creation": "Création de la table", "model_creation": "Création du modèle", "metadata_creation": "Création des métadonnées du modèle", "link_creation": "Liaison de la table et du modèle", "table_ingestion": "Ingestion des données dans la table", "model_ingestion": "Ingestion des données dans le modèle"}, "SummaryTitle": "Ingestion rapide terminée", "SummaryDescription": "Voici un résumé des actions effectuées. Si une des actions a échoué, vous pouvez la relancer depuis le menu.", "NewQuickIngest": "Nouvelle ingestion rapide", "SeeContent": "Voir les données ingérées", "SeeModelContent": "Voir le contenu du modèle", "SeeTableContent": "Voir le contenu de la table", "OtherFeaturesTitle": "Plus d'options", "OtherFeaturesDescription": "Si vous voulez gérer manuellement l'ingestion du CSV, ou si vous voulez mettre à jour les données ingérées, vous pouvez utiliser les options spécifiques en cliquant sur le bouton ci-dessous. Si vous choisissez de réaliser une ingestion manuellement, assurez-vous d'effectuer les étapes dans l'ordre."}, "extractKeywords": "Extraire mots clés", "addKeywordsFromMetadata": {"Title": "Extraire des mots clés", "Description": "Permet d'extraire des mots clés depuis une métadonnée."}, "moreInfo": "Plus d'informations", "TableContentDescription": "Lors de l'ingestion, toutes les données brutes du fichier CSV sont transférées dans une table en base de données. Ces données ne sont pas directement accessibles ni affichées, mais sont utilisées pour créer les métadonnées des items en suivant le modèle créé.", "ModelContentDescription": "Une fois l'ingestion terminée, les métadonnées des items sont créées en suivant le modèle. Ce sont ces données qui sont affichées au sein d'ArcheoGRID. Seulement les items qui ont été traité/trouvé sont affichés. Vous pouvez mettre à jour les métadonnées des items en cliquant sur le bouton 'Ingérer' en haut. Vous pouvez aussi extraire des mots clés depuis une métadonnée en cliquant sur le bouton 'Extraire mots clés'."}, "thesaurus": {"link": {"form": {"type": "Type de thesaurus :", "project": "Projet :"}}}, "user": {"history": {"type": "Type de modifications"}}}}, "scribe": {"noUserModel": "Aucun modèle accessible", "noAssignedUser": "Aucun utilisateur accessible", "noAssignedProjects": "Aucun projet accessible"}, "errors": {"user_not_exist": "L'utilisateur n'existe pas", "bad_password": "Mot de passe incorrect"}, "advanced_model": {"actor_literal_label": "Nom complet", "actor_literal_placeholder": "ex: <PERSON> ou CNRS", "actor_identifier_label": "URI", "actor_identifier_placeholder": "(orcid, viaf...) ex: http://viaf.org/123456789", "actor_type_label": "Type d'acteur", "actor_type_person_label": "<PERSON><PERSON>", "actor_type_organization_label": "Organisation", "actor_mandatory_error": "Nom et type de l'acteur obligatoire", "actor_invalidUri_error": "URI non valide", "actor_modify_actor_warning": "Attention, modification d'un acteur existant. Pour créer un nouvel acteur, entrer un autre nom.", "datation_date_min_label": "Date de début", "datation_date_min_placeholder": "ex: 2024-01-01 ou 2024-01 ou 2024", "datation_date_max_label": "Date de fin", "datation_date_max_placeholder": "ex: 2024-12-31 ou 2024-12 ou 2024", "datation_date_literal_label": "Date litéral", "datation_date_literal_placeholder": "ex: L'ensemble de l'année 2024", "datation_mandatory_error": "La date de début de la datation est obligatoire", "datation_invalidDate_error": "Utiliser le format AAAA-MM-JJ ou AAAA-MM ou AAAA", "datation_invalidDateMax_error": "Utiliser le format AAAA-MM-JJ, AAAA-MM, AAAA ou laisser la date de fin vide", "location_name_label": "Nom de la localisation", "location_name_placeholder": "ex: Centre ville de Bordeaux", "location_geonames_label": "Code geonames", "location_geonames_placeholder": "ex: 6455058", "location_latlng_label": "Coordonnées GPS", "location_latlng_placeholder": "ex: 44.833333,-0.566667", "location_mandatory_error": "Au moins un des champs de la localisation doit étre rempli", "location_geonames_error": "Code geonames non valide"}, "addKeyword": {"title": "Ajouter mot-clé", "addingMode": "Mode d'ajout", "addFromMetadata": {"title": "Ajout depuis une metadonnée", "metadataName": "Nom de la métadonnée", "form": {"inMetadataLabel": "Métadonnée d'origine", "inMetadataDescription": "Choisir la métadonnée à partir de laquelle les mots clés vont être extraits.", "delimiterLabel": "Délimiteur", "delimiterDescription": "<PERSON><PERSON> le délimiteur des mots clés", "thesaurusLabel": "Thesaurus", "thesaurusDescription": "Choisir le thesaurus des mots clés", "thesaurusSimpleWarning": "Le type de thesaurus simple ne permet de lier qu'un seul concept à l'item. Si la métadonnée contient plusieurs concepts, seul le premier sera utilisé.", "outMetadataLabel": "Métadonnée de sortie", "outMetadataDescription": "Choisir la métadonnée dans laquelle les mots clés vont être ajoutés. Vous pouvez aussi définir les mots clés comme libres.", "outMetadataFreeTags": "Mots clés libres", "outMetadataCreateTags": "<PERSON><PERSON><PERSON> les mots clés libres inexistants", "outMetadataFilters": "Filtres"}, "results": {"parameters": "Paramètres", "results": "Résultats", "ignoredItems": "Items ignorés", "foundThes": "Mots clés trouvés dans le thesaurus", "foundFreeTags": "Mots clé libres existants", "createdTags": "Mots clé libres créer", "notFoundTags": "Mots clé non trouvés"}, "outMetadataError": "Aucune métadonnées trouvées à partir des filtres"}, "addFromScratch": {"title": "Ajout libre"}}, "Restart": "Recommencer", "3dmodel": "Modèle 3D", "3dmodellower": "modèle <PERSON>", "3dmodels": "<PERSON>d<PERSON><PERSON>", "3dmodeldescription": "Description du modèle 3D", "8charmin": "8 caractères minimum", "a": "un", "ae": "une", "about": "concernant", "actions": "Actions", "actor": "Acteur / Personne", "actorexists": "Cette personne existe déjà (même nom, même prénom)", "activity": "Activité", "add": "Ajouter", "addCollection": "Ajouter une collection", "added": "<PERSON><PERSON><PERSON>", "addactor_success": "Acteur c<PERSON><PERSON> avec succès", "addgroup_success": "Groupe créé avec succès", "addObjVirt": "Ajouter un objet virtuel au dépôt", "address": "<PERSON><PERSON><PERSON>", "addSearchbar": "Ajouter une nouvelle barre de recherche (ET)", "addactor_head": "Ajout d'un acteur / agent / personne", "addorganization_head": "Ajout d'une organisation", "addorganization_success": "Organisation créée avec succès", "addthesauruselem": "A<PERSON>ter ou traduire un concept", "adduser_head": "Création d'un utilisateur", "adduser_success": "Utilisateur c<PERSON>é avec succès", "addvirtualfolder": "Ajouter un dossier", "addzip": "Ajouter à mon archive pour télécharger plus tard", "advancedSearch": "Recherche avancée", "agent": "l'un de ses mandataires", "aligned": "align<PERSON>", "alignedPlural": "alignés", "all": "Toutes les", "allFields": "Tous les champs", "allfolderitems": "tous les items du répertoire", "allow": "autoriser", "already": "<PERSON><PERSON><PERSON><PERSON>", "amharique": "amharique", "AND": "ET", "and": "et", "annotation": "Annotation", "archeogriddef": "outil collaboratif pour gérer toute la documentation (iconographie, relevés...) des projets en Humanités Numériques (SHS) intégrant de la 3D : annotation, indexation, préservation, sauvegarde, dissémination", "askproject": "Demander l'attribution d'un projet à votre administrateur", "assigned": "attribué", "associated": "associé", "associatedFile": "fichier associé", "associatedFileplur": "fichiers associés", "associatedObject": "object associé", "associatedObjectplur": "objects associés", "at": "à", "atleast": "au moins", "aton3dtext": "Reconstituition de la ville d'Amarna, dédiée au culte d'Aton", "author": "<PERSON><PERSON><PERSON>", "authorizedFolder": "Dossier(s) autorisé(s)", "back": "Retour", "backprojectpage": "Retour à la page du projet", "backtotop": "<PERSON><PERSON> de page", "begin": "Commence par", "beginEntertext": "Commencer à entrer du texte", "bienvenue": "Bienvenue ", "branch": "branche", "browse": "Parcourir", "browseThesauri": "Parcourir les thesaurus", "browseThesaurus": "Parcourir le thesaurus", "by": "par ", "byteam": "par l'équipe d'Archeovision", "cancel": "Annuler", "changeOrder": "Changer l'ordre", "checkAll": "Tout cocher", "checkPage": "Cocher la page", "checkAuthorRightsLeave": "Je possède tous les droits sur ces documents et dans le cadre de ce projet uniquement je les cède à des fins d'utlisation pour les scientifiques", "choose": "Choi<PERSON>", "chronology": "Chronologie", "citation": "citation", "click": "cliquer", "close": "<PERSON><PERSON><PERSON>", "cnd3d": "Conservatoire National des données 3D", "cnd3dInfo": "Le conservatoire national des données 3D SHS", "collapseFolders": "Réduire les répertoires", "collection": "Collection", "collectionexists": "Cette collection existe déjà", "collections": "Collections", "foldersHeader": "Dossiers", "codecorpus": "Code Corpus", "codeproject": "Code projet", "comment": "Ajouter un commentaire", "concept": "le concept", "concerned": "<PERSON><PERSON>", "concerned_plur": "concernés", "confirm": "Confirmer", "confirmDeleteAll": "Supprimer tout le contenu de ce dossier virtuel ?", "confirmpassword": "Confirmer le mot de passe", "connecte": "Se connecter", "connecteArcheoGRID": "Se connecter avec ArcheoGRID", "connecteNH": "Se connecter avec HumanID", "content": "Contenu", "copyrights": "Credits pour les droits d'auteur", "corpus": "le corpus", "Corpus": "Les corpus d'ArcheoGRID", "corpustext": "Les corpus iconographiques d'Archeovision", "country": "Pays", "create": "c<PERSON><PERSON>", "Create": "<PERSON><PERSON><PERSON>", "createGroup": "Créer un groupe", "createUser": "C<PERSON>er un utilisateur", "creationDate": "date de création", "crowdsourcing": "Documentation participative", "crowdsourcingModeration": "Les images téléchargées ne seront visibles sur le site que si elles ont été validées par nos modérateurs scientifiques", "deconnecte": "Se déconnecter", "dashboard": "Tableau de bord", "data": "donn<PERSON>", "datation": {"date_min_label": "D<PERSON>but", "date_max_label": "Fin", "date_literal_label": "Littéral"}, "date": "Date", "dateExplicite": "Pour la date du", "delete": "supprimer", "deleted": "supprimées", "Delete": "<PERSON><PERSON><PERSON><PERSON>", "deleteAll": "Supp<PERSON>er tout", "deleteSuccessful": "Suppression de la sélection réussie", "deposit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "depositor": "Entité déposante", "depotname": "Nom pour le dépôt", "depotfinal": "Finaliser <PERSON> <PERSON>ép<PERSON>t", "different": "<PERSON>ff<PERSON><PERSON> de", "digitalResource": "Ressource numérique", "digitalResources": "Ressources numériques", "dirname": "Do<PERSON><PERSON> <PERSON>", "disconnected": "Vous êtes déconnecté.", "displayGrid": "Afficher grille", "displayList": "<PERSON><PERSON><PERSON><PERSON> liste", "displayMode": "Mode d'affichage (grille/liste)", "documentsDisplayed": "Documents affichés", "documentsSelected": "Documents sélectionnés", "selected": "Sélectionnés", "download": "Télécharger ", "eachFields": "Tous les champs", "edit": "Modifier", "editGroup": "Modifier un groupe", "editUser": "Modifier un utilisateur", "emptyName": "Le nom est vide", "enrichData": "Ajouter des métadonnées", "enter": "<PERSON><PERSON><PERSON>", "enterDate": "Date d'entrée", "entity": "Entité", "equal": "<PERSON><PERSON>", "eraseLot": "Ec<PERSON>r les données existantes ?", "eraseSearch": "Effacer tous les paramètres de recherche", "eraseTree": "Effacer les répertoires selectionnés", "error": "<PERSON><PERSON><PERSON>", "EuropeanaRights": "Droits Europeana", "event": "événement", "everyExtensions": "Extensions", "everyFolders": "Tous les répertoires", "expandFolders": "Développer les répertoires", "extp3d": "Projets 3D Collaboratifs", "extp3d_sing": "Projet 3D Collaboratif", "extp3dtext": "ArcheoGRID dédié aux projets 3D SHS", "file": "<PERSON><PERSON><PERSON>", "file2": "le fi<PERSON>er", "files": "fichiers", "fileEnterDate": "Date d'entrée du fichier dans ", "fileExtension": "fileExtension", "filename": "Nom du fichier", "filesize": "<PERSON><PERSON>", "fileSynchro": "Synchronization de fichiers", "filesSynchro": "Synchronization des fichiers en base de données", "fileupload": "<PERSON><PERSON><PERSON> <PERSON>(s)", "fillerror": "Erreur dans le formulaire", "fillform": "<PERSON><PERSON><PERSON> de remplir le formulaire.", "firstDepotDemand": "<PERSON><PERSON><PERSON>", "folder": "dossier", "folders": "dossiers", "for": "pour", "forbid": "interdire", "forbidden": "Pas autorisé - Pas assez de droits", "forbiddenFolder": "Dossier(s) non autorisé(s)", "forwriting": "en écriture", "freetags": "tags libres", "from": "de", "From": "De", "generalType": "Type général", "give": "<PERSON><PERSON>", "globalFile": "Fiche globale", "goandsee": "Aller voir", "group": "le groupe", "groupSimple": "groupe", "groupsList": "Liste des groupes", "groupPermissions": "Permissions pour le groupe", "groups": "Groupes", "groupname": "Nom du groupe", "havetoconnect": "Vous devez vous connecter pour accéder au service", "heritageAsset": "Bien patrimonial", "heritageAssetType": "Type de bien patrimonial", "hide": "Masquer", "idParentNewThesaurusElem": "Identifiant de l'élément parent dans le thesaurus", "Identifier": "Identifiant", "identifier_other": "Autre identifiant (idREF, idHAL...)", "identifier_other_comment": "Donner l'URI complète de l'identifiant. Par exemple", "imagesize": "<PERSON>lle de l'image", "in": "dans", "include": "Contient", "index": "Indexer", "indexed": "indexé", "indexItems": "item(s) indéxé(s) ", "infoDepot": "Vous pouvez déposer plusieurs documents à la fois s'ils ont approximativement les mêmes dates (1 seul formulaire à remplir dans ce cas).", "information": "Information", "inNameOf": "Pour le compte de ", "insertImpossible": "Ajout du dossier impossible", "itemType": "itemType", "karnaktext": "ArcheoGRID Karnak, dédié aux données des temples de Karnak, gé<PERSON>es par le Cfeetk", "keyword": "Mot-clé", "keywordUpdated": "<PERSON><PERSON><PERSON><PERSON><PERSON> ajouté ou mis à jour", "keywords": "Mots-clés", "license": "Licence", "licenses": "les licences", "link": "<PERSON><PERSON>", "location": "Localisation", "loginbad": "<PERSON><PERSON>, <PERSON><PERSON><PERSON> chose s'est mal passé lors du login", "mail": "Adresse e-mail", "mailcontact": "Adresse e-mail (publique) pour tout contact", "mailto": "envoyer un mail", "maintenance": "Site en maintenance", "mandatory": "Champ obligatoire", "mandatoryMessageCheck": "<PERSON><PERSON><PERSON> de sélectionner au moins une option", "menu": "menu", "Menu": "<PERSON><PERSON>", "message_addSelection": "Erreur pendant l'ajout de la Sélection, des doublons ont été trouvés... Supprimez les doublons dans le dossier virtuel ou dans la sélection et recommencez ... ", "message_notlogged": "Vous devez être logué pour accéder au document", "message_notaccess": "Vous n'avez pas accès à ce document", "message_norightstopath": "Vous n'avez les droits pour modifier ce document", "message_errordelete": "Erreur pendant la suppression", "message_norightstodel": "Vous n'avez pas les droits pour supprimer ce document", "metadata": "Métadonnées", "model": "<PERSON><PERSON><PERSON><PERSON>", "moreinfo": "Plus d'information", "moreinfotext": "Voir le site d'Archeovision / Archeovision production", "multipleIndex": "Indexation multiple", "multipleIndexing": "Indexation multiple de tout le répertoire", "name": "Nom", "Name": "Nom", "fname": "First name", "lname": "Last name", "nameNewThesaurusElem": "Nom du nouveau terme de thesaurus", "new": "Nouveau", "newDepotDemand": "Faire un dépô<PERSON>", "newDepotDemand2": "<PERSON><PERSON><PERSON>r un autre fichier", "newProject": "Nouveau projet de site", "newOP": "Nouveau project scientifique", "no": "Non", "no2": "Pas de", "no4": "Pas d'", "no3": "Non", "nologin": "Pas encore de compte ? nous contacter", "noFolders": "Pas de répertoires", "noVirtualFolders": "Aucune collection", "nodata": "Il n'y a pas encore de métadonnées", "nodirectory": "Please only upload file(s), no directories (folder).", "none": "aucun", "None": "Aucun", "note": "Commentaire", "notFound": "Pas trouvé - N'existe pas ou supprimé", "number": "Nombre", "object": "Objet", "object2": "l'objet", "objectName": "Nom de l'objet", "objectEnterDate": "Date d'entrée de l'objet dans ", "objectVirtuel": "Objet(s) Virtuel(s)", "of": "de", "of2": "du", "of3": "<PERSON> la", "of4": "d'un", "of5": "de l'", "ofpluriel": "des", "one": "un", "only": "seulement", "or": "ou", "organization": "organisation", "Organization": "Organisation", "organizationexists": "Cette organisation existe déjà", "organizationname": "Nom de l'organisation", "organizationName": "Nom de l'organisation", "other": "Autres", "overall": "projets multi-sites", "overallprojects": "Projets multi-sites", "overallproject": "Projet multi-sites", "passport": "Passeport", "password": "Mot de passe", "passwordbad": "Mot de passe incorrect", "period": "Période", "person": "personne", "Person": "<PERSON><PERSON>", "phone": "Telephone", "places": "<PERSON><PERSON>", "placeholderdepot": "<LIEU GEOGRAPGIQUE>_<OBJET DU DEPOT>", "polygonal_selection": "Cliquez sur l'image pour ajouter des points.", "private": "Priv<PERSON>", "project": "Projet", "Project": "Projet", "projectsing": "un projet", "projectplur": "Projets", "projectpage": "Page du projet", "projects": "Les projets 3D d'Archeovision", "public": "Publique", "readRights": "Droits en lecture seule", "readWriteRights": "Droits en lecture et écriture", "registerSelectionSuccess": "Enregistrement de la sélection réussi", "removeCollection": "Supp<PERSON><PERSON> de la collection", "removeSearchbar": "Supprimer la nouvelle barre de recherche", "representing": "représentant", "representative_image": "Image représentative pour le projet", "required": "Champ obligatoire", "requisit_for_deposit": "Si vos données 3D ont été produites dans le cadre d'un projet de recherche en SHS, elles peuvent être déposées dans le conservatoire. Pour cela il faut disposer d'un compte utilisateur dans le conservatoire.", "reset": "Réinitialiser", "resource": "la ressource", "resourceMaj": "Ressource", "resources": "les ressources", "result": "Résultat", "returnHome": "Retourner à l'accueil", "rights": "Droits", "sameDate": "Si vous connaissez la date exacte de production du ou des documents à déposer, merci d'entrer la même date dans les deux champs date.", "save": "Enregistrer", "savechange": "Sauvegarder les changements", "saveSearch": "Sauvegarder la recherche", "saveUnico": "Sauvegarder l'unico", "savedSearches": "Recherches Enregistrées", "savedSearchError": "Votre recherche n'a pas pu être enregistrée.", "savedSearchName": "Quel nom voulez-vous donner à votre recherche ?", "savedSearchSuccess": "Votre recherche a été enregistrée avec succès.", "search": "<PERSON><PERSON><PERSON>", "searchAllThesaurus": "Rechercher dans tous les thesaurus", "asearch": "Recherche", "see": "Voir", "selectedZone": "Zone sélectionnée", "selectionDelete": "Supprimer sélection", "myselection": "Ma sélection", "selectionName": "V<PERSON> devez donner un nom à l'unico.", "selectionSave": "Enreg. sélectionnés", "selectionOpen": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>lection", "selectionSuccess": "Unico enregistré.", "send": "Envoyer le message", "signature": "Nom complet pour signature de l'utilisateur, utilisé dans les commentaires, les informations pour les identifiants pérennes, ...", "signup_short": "S'enregistrer", "signup": "Demande d'ouverture de compte au Conservatoire National des Données 3D SHS", "signupbademail": "<PERSON>eu<PERSON>z entrer une adresse mail valide", "signupbadpassword": "Les 2 mots de passe ne coï<PERSON>ident pas, ve<PERSON><PERSON><PERSON> réessayer", "signupfailure": "Impossible de créer le compte, ve<PERSON><PERSON><PERSON> réessayer", "signupko": "Je n'ai pas de compte (en demander un)", "signupok": "J'ai un compte", "signupsuccess": "Votre demande de compte a été transmise.", "simpleSearch": "Recherche simple", "simple": "simple", "size": "Volumétrie attendue pour les données 3D", "sourceFile": "fichier source", "soutien HN": "Avec le soutien d'Huma-Num", "spatial_coverage": "Couverture spatial", "start": "Commencer ", "start2": "Commencer à", "status": "statut", "Status": "Statut", "subject": "Sujets", "submit": "Soumettre le formulaire", "success": "Opération réussie", "synchronize": "synchroniser", "synchronized": "synchronisées", "synchronizeName": "la synchronisation", "synchRec": "Activer la récursivité - synchronisation pour tous les sous-dossiers", "synchRecInfo": "Récursivement :", "synchResult": "Références en base de données", "synchResultFolder": "Nombre de dossiers synchronisés : ", "synchResultFile": "Nombre de fichiers synchronisés : ", "synchResultEmpty": "Pas de nouvel élément à synchroniser", "synchResultDelete": "Nombre de références en base de données", "synchResultMayDelete": "qui auraient pu être ", "synchLastTime": "Dernière étape", "synchTimeInfo": "<PERSON><PERSON> avez exécuté", "synchExecute": "<PERSON><PERSON>i d'exécuter à nouveau", "synchExecuteButton": "Exécuter", "talatattext": "Travaux initiés par <PERSON> sur les talatat découvertes à Amarna", "temp_user_head": "Utilisateurs temporaires en attente de création", "thanksfillform": "Merci d'avoir rempli le formulaire", "the": "le", "the2": "la", "the3": "l'", "thepluriel": "les", "thesaurusManagement": "Gestion de thesaurus", "thesaurus": "le thesaurus", "thesaurus_name_label": "Nom du thesaurus (code)", "thesaurusOnly": "thesaurus", "thesaurusType": "Type de thesaurus", "this": "ce", "time": "fois", "title": "Titre", "to": "à", "to2": "au", "translateConcept": "Donner la traduction pour un concept", "translation": "Traduction", "translationLng": "Langue de la traduction", "translationName": "Concept à traduire", "type": "taper", "types": "Types", "txt_link_button": "<PERSON><PERSON>", "txt_integrate": "Intégrer les items de la selection dans un dossier virtuel : ", "txt_integrate_button": "Intégrer", "txt_integrate_obj": "<PERSON><PERSON>, associer les items de la selection à un objet : ", "txt_illustrate_obj": "Utiliser cet item pour illustrer l'objet : ", "txt_illustrate_button": "<PERSON><PERSON><PERSON><PERSON>", "uncheckAll": "<PERSON><PERSON>", "uncheckPage": "Décocher la page", "unicoDeleteSuccess": "Unico correctement effacé", "unicoType": "Type d'unico (forme)", "rawUnicoData": "Données brute de l'unico", "updateDate": "Date de mise à jour", "upload": "dé<PERSON><PERSON>", "user": "Utilisa<PERSON>ur", "user2": "l'utilisateur", "userconnected": "Nombre d'utilisateurs connectés", "userDeleted": "Utilisateur Supprimé !", "userexists": "Cet utilisateur existe déjà", "userEntity": "Entité de rattachement", "usersList": "Liste des utilisateurs", "userPermissions": "Permissions d'un utilisateur", "username": "Nom d'utilisateur", "userparam": "Paramètres", "usernb": "Nombre d'utilisateurs", "validate": "valider", "visitnb": "Nombre de visites", "visualization": "Visualisation", "visualizeItem": "Visualiser les items dans un nouvel onglet", "which": "quel", "with": "avec", "writeRights": "Droits en écriture", "wrongProject": "Ce projet n'existe pas", "yes": "O<PERSON>", "invalidCoordinates": "invalidCoordinates", "showImage": "showImage", "open_new_tab": "open_new_tab", "found": "found", "view": "view", "openNewTab": "openNewTab", "more": "plus", "backToProject": "Retour au projet", "selectionTitle": "Sélection", "selectionDescription": "Description de la sélection", "selectionFor": "Sélection pour", "items": "Éléments", "display": "<PERSON><PERSON>", "selectionControls": "Outils de sélection", "displayOptions": "Paramètres d'affichage", "gridView": "Vue en grille", "listView": "Vue en liste", "itemsPerPage": "Éléments par page", "saveSelection": "Enregistrer la sélection", "noItemsSelected": "Aucun élément sélectionné", "selectAtLeastOne": "Veuillez sélectionner au moins un élément", "selectOnlyOne": "Veuillez sélectionner un seul élément", "virtualFolders": "Dossiers virtuels", "createVirtualFolder": "<PERSON><PERSON><PERSON> un dossier virtuel", "noResultsFound": "Aucun résultat trouvé", "ofPages": "sur {{count}} pages", "selectAll": "<PERSON><PERSON>", "fewerItemsPerRow": "Moins d'éléments par ligne", "moreItemsPerRow": "Plus d'éléments par ligne", "loadingMapData": "Chargement des données cartographiques...", "checkingStreetView": "checkingStreetView", "streetViewNotAvailable": "streetViewNotAvailable", "showingDefaultMap": "showingDefaultMap", "errorLoadingMap": "Erreur lors du chargement de la carte.", "viewInGoogleMaps": "viewInGoogleMaps", "viewLargerMapOSM": "viewLargerMapOSM", "seeMapBelow": "seeMapBelow", "errorLoadingMapLibrary": "Erreur lors du chargement de la bibliothèque cartographique.", "loadingMap": "Chargement de la carte...", "exploreRandomNoResult": "Aucun résultat aléatoire trouvé.", "noResult": "Aucun résultat trouvé.", "errorOccured": "Une erreur est survenue.", "emptyResponse": "Aucune donnée à afficher.", "nbResultsText": "documents trouvés", "no_title": "Pas de titre", "not_authorized": "Non autorisé", "normalView": "Vue normale", "compactView": "Vue compacte", "selectCollection": "Sélectionnez une collection", "selectObject": "Sélectionnez un objet", "resetSelection": "Réinitialiser la sélection", "browsePACTOLS": "Parcourir les PACTOLS", "loading": "loading", "allThesauri": "<PERSON><PERSON> les thesaurus", "collection_label": "Collection", "all_collections": "Toutes les collections", "Next": "Suivant", "Previous": "Précédent", "copyMetadata": "Copier les metadonnées", "pasteMetadata": "Coller les metadonnées", "Ingest": "<PERSON><PERSON><PERSON>", "File": "<PERSON><PERSON><PERSON>", "Folder": "Dossier", "Object": "Objet", "AllFP": "Toutes", "Branch": "Branche", "Today_at": "Aujou<PERSON>'hui à", "ModelDescription": "Description du modèle", "Table": "Table", "Model": "<PERSON><PERSON><PERSON><PERSON>", "Delimiter": "Délimiteur", "Step": "Étape", "selectFolder": "Selectionnez un dossier", "dimensions": "dimensions", "searchFolder": "searchFolder", "tagsThesaurus": "Tags de thesaurus", "select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "searchThesaurusFolder": "searchThesaurusFolder"}