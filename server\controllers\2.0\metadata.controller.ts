import type { IDatabase } from "pg-promise";
import { z } from "zod";

import { defined } from "~/helpers/tools";
import { t } from "~/trpcContext";
import type { Branch } from "~/types/api";
import { branch, id, language } from "~/types/schemas";
import {
  allMetadataOutput,
  find_all_output,
  find_one_output,
  getMetadataListIdOutput,
  getMetadataListOutput,
  getMetadataModelGeneralOutput,
  getMetadataModelOutput,
  getMetadataModelProjectOutput,
  getMetadataOutput,
  getMetadataWithTypeOutput,
  metadata_type,
  patchMetadataValueInput,
  type,
  updateValuesInput,
  metadata,
  copypasteItem
} from "~/types/schemas/metadata";

import thesaurusController from "./thesaurus.controller";
import { query } from "express";

const check_metadata_exists = async (database: IDatabase<unknown>, branch: Branch, id: string | number) => {
  const metadata = await database.oneOrNone(`SELECT id FROM ${branch}_metadata WHERE id = $1`, id);

  if (!metadata) {
    throw new Error(`Failed to insert passport value. Metadata ${id} does not exist!`);
  }
};

export default t.router({
  /**
   * Get all the metadata models
   * @param branch The affected branch
   * @param language The metadata's label language
   * @returns The metadata models
   */
  findAll: t.procedure
    .input(z.object({ branch, language }))
    .output(find_all_output)
    .query(async ({ input: { branch, language }, ctx: { database } }) => {
      const data = await database.any(
        `SELECT * FROM ${branch}_metadata_model m
        INNER JOIN ${branch}_metadata_model_label ml ON m.id = ml.id_metadata_model
        WHERE ml.language = $1 AND m.visible = 1`,
        language,
      );

      return find_all_output.parse(data);
    }),
  /**
   * Get one metadata model
   * @param branch The affected branch
   * @param language The metadata's label language
   * @param model The name of the model
   * @returns The metadata models
   */
  findOne: t.procedure
    .input(z.object({ branch, language, model_id: id }))
    .output(find_one_output)
    .query(async ({ ctx, input }) => {
      const data = await ctx.database.one(`SELECT * FROM ${input.branch}_metadata_model WHERE id = $1`, [
        input.model_id,
        input.language,
      ]);

      return find_one_output.parse(data);
    }),
  getMetadataInfo: t.procedure
    .input(z.object({branch, metadata_id: id, language}))
    .query(async ({ctx, input}) => {
      const status = await ctx.database.one(
        `SELECT m.status, ml.label, m.code, m.y as is_mandatory from ${input.branch}_metadata m inner join ${input.branch}_metadata_label ml on m.id = ml.id_metadata WHERE id = ${input.metadata_id} and ml.language = '${input.language}'`,);

      return z.object({status: z.string(), label: z.string(), code: z.string()}).parse(status)
    }),
  /**
   * Get all the metadata models for a project
   * @param branch The affected branch
   * @param language The metadata's label language
   * @param id The id of the project
   * @returns The metadata models
   */
  allMetadata: t.procedure
    .input(
      z.object({
        branch,
        language,
        id: z.number().transform((n) => n || null),
        type,
      }),
    )
    .output(z.any().and(allMetadataOutput))
    .query(async ({ input, ctx }) => {
      if (!input.id) {
        return null;
      }

      // if model does not exist, break return null => Why ??? on récupère l'id d'un item et non l'id d'un modèle !
      // if item does not exist, break return null
      const item_exists = await ctx.database.oneOrNone(
        `SELECT id FROM ${input.branch}_${input.type} WHERE id = $1`,
        input.id,
      );

      if (!item_exists) {
        return null;
      }

      // nouvelle fonction pour récupérer les tag de thesaurus pactols en s'affranchissant des modeles de métadonnées :
      // les thesaurus ne sont pas dans le modèle de métadonnées mais indépendants => V3 de la fonction
      const querythespactols = "SELECT get_info_thespactols_gen4($1, $2, $3) as thespactolsinfo";
      const querythesmaison = `SELECT get_info_thes_gen2('${input.language}', $1, $2, $3) as thesmaisoninfo`;
      const querynomenclature = `SELECT get_info_nomen_gen('${input.language}', $1, $2, $3) as nomeninfo`;
      // on récupère le thes_path en plus pour gerer la polyhierarchie
      const querymulti = `SELECT get_info_multi_gen5('${input.language}', $1, $2, $3) as multiinfo`;
      const queryPeriodo = `SELECT get_info_periodo_gen('${input.language}', $1, $2, $3) as periodoinfo`;

      const query = await ctx.database.oneOrNone(
        `SELECT get_all_metadata_json_v9('${input.language}', $1, $2, $3) as myquery`,
        [input.id, input.branch, input.type],
      );

      const result = await ctx.database.oneOrNone("SELECT $1:raw", query.myquery);

      if (!result){
        console.log("NO RESULT RETURN FROM GET_ALL_METADATA_JSON_V9");
        return null;
      }

      let promises = [];
      promises.push(
        ctx.database.any(querythespactols, [input.id, input.branch, input.type]).then((pactols) => result.pactols = pactols)
      );
      promises.push(
        ctx.database.any(querynomenclature, [input.id, input.branch, input.type]).then((nomenclature) => result.nomenclature = nomenclature)
      );
      promises.push(
        ctx.database.any(queryPeriodo, [input.id, input.branch, input.type]).then((periodo) => result.periodo = periodo)
      );
      promises.push(
        ctx.database.any(querymulti, [input.id, input.branch, input.type]).then((multi) => result.multi = multi)
      );
      promises.push(
        ctx.database.any(querythesmaison, [input.id, input.branch, input.type]).then((thesaurus) => result.thesaurus = thesaurus)
      );

      switch (input.type) {
        case "object":
          // OBJECT - OBJECT
          promises.push(
            ctx.database.manyOrNone(`
              SELECT o.id, o.name
              FROM ${input.branch}_object_object oo
              INNER JOIN ${input.branch}_object o ON o.id = oo.id_object_min
              WHERE oo.id_object_ref = ${input.id}
              ORDER BY o.id`
            ).then((linkedObjects) => {
              result.linkedObjects = linkedObjects
            })
          );
          // OBJECT - FILE
          promises.push(
            ctx.database.manyOrNone(`
              SELECT f.id, f.name
              FROM ${input.branch}_object o
              INNER JOIN ${input.branch}_file_object fo ON fo.id_object = o.id AND fo.id_file != o.id_file_representative
              INNER JOIN ${input.branch}_file f ON f.id = fo.id_file
              WHERE fo.id_object = ${input.id}
              ORDER BY f.id`
            ).then((linkedFiles) => {
              result.linkedFiles = linkedFiles
            })
          );
          // OBJECT - FOLDER
          promises.push(
            ctx.database.manyOrNone(`
              SELECT f.id, f.name
              FROM ${input.branch}_object o
              INNER JOIN ${input.branch}_folder_object fo ON fo.id_object = o.id AND fo.id_folder != o.id_folder
              INNER JOIN ${input.branch}_folder f ON f.id = fo.id_folder
              WHERE fo.id_object = ${input.id}
              ORDER BY f.id`
            ).then((linkedFolders) => {
              result.linkedFolders = linkedFolders
            })
          );
          break;
        case "file":
          // FILE - OBJECT
          promises.push(
            ctx.database.manyOrNone(`
              SELECT o.id, o.name
              FROM ${input.branch}_file f
              INNER JOIN ${input.branch}_file_object fo ON f.id = fo.id_file
              INNER JOIN ${input.branch}_object o ON o.id = fo.id_object AND o.id_file_representative != fo.id_file
              WHERE f.id = ${input.id}
              ORDER BY o.id`
            ).then((linkedObjects) => {
              result.linkedObjects = linkedObjects
            })
          );

          // FILE - FOLDER
          promises.push(
            ctx.database.manyOrNone(`
              SELECT fo.id, fo.name
              FROM ${input.branch}_file fi
              INNER JOIN ${input.branch}_file_folder fifo ON fi.id = fifo.id_file AND fifo.id_folder != fi.id_folder
              INNER JOIN ${input.branch}_folder fo ON fo.id = fifo.id_folder
              WHERE fi.id = ${input.id}
              ORDER BY fo.id`
            ).then((linkedFolders) => {
              result.linkedFolders = linkedFolders
            })
          );

          // UNICO
          promises.push(
            ctx.database.manyOrNone(`
              SELECT u.id, u.name, u.annotation, ARRAY_AGG(tm.name) as tags
              FROM ${input.branch}_unico u
              INNER JOIN ${input.branch}_thesaurus_multi_item tmi ON tmi.id_item = u.id AND tmi.item_type = 'unico'
              INNER JOIN ${input.branch}_thesaurus_multi tm ON tmi.id_thesaurus = tm.id AND tmi.id_thes_thesaurus = tm.id_thes
              WHERE u.id_file = ${input.id}
              GROUP BY u.id, u.name, u.annotation
              ORDER BY u.id`
            ).then((linkedUnicos) => {
              result.linkedUnicos = linkedUnicos
            })
          );
          break;
        case "unico":
          // FILE
          promises.push(
            ctx.database.oneOrNone(`
              SELECT f.id, f.name
              FROM ${input.branch}_unico u
              INNER JOIN ${input.branch}_file f ON f.id = u.id_file
              WHERE u.id = ${input.id}`
            ).then((linkedFile) => {
              result.linkedFile = linkedFile;
            })
          );

          // FOLDER
          promises.push(
            ctx.database.manyOrNone(`
              SELECT f.id, f.name
              FROM ${input.branch}_unico u
              INNER JOIN ${input.branch}_folder_unico fu ON u.id = fu.id_unico
              INNER JOIN ${input.branch}_folder f ON f.id = fu.id_folder
              WHERE u.id = ${input.id}
              ORDER BY f.id`
            ).then((linkedFolders) => {
              result.linkedFolders = linkedFolders
            })
          );
          break;
        default:
          break;
      }

      await Promise.allSettled(promises).then((results) => {
        results.forEach((r) => {
          if (r.status !== "fulfilled") {
            console.log(`Error in metadata controller for ${input.type}-${input.id} : ${r.reason}.`); 
          }
        })
      }
      );

      return result;
    }),
  getMetadata: t.procedure.input(z.object({ branch, language, model: z.string() })).query(async ({ ctx, input }) => {
    const metadata = await ctx.database.manyOrNone(
      `SELECT m.*, l.* FROM ${input.branch}_metadata m
        INNER JOIN ${input.branch}_metadata_label l ON l.id_metadata = m.id
        INNER JOIN ${input.branch}_metadata_model mo ON mo.id = m.id_metadata_model
        WHERE mo.name = $1 and language = $2 ORDER BY rank`,
      [input.model, input.language],
    );

      return getMetadataOutput.nullable().parse(metadata);
    }),

  getMetadataByCode: t.procedure
    .input(z.object({ branch, code: z.string() }))
    .query(async ({ ctx, input }) => {
      const result = await ctx.database.one(
        `SELECT * FROM ${input.branch}_metadata WHERE code = '${input.code}'`
      );

      return metadata.parse(result);
    }),

  /**
   * Get metadata models filtered by type
   * @param branch The affected branch
   * @param language The metadata's label language
   * @param type The data type of the models
   * @param model The name of the model
   * @returns The metadata models
   */
  getMetadataWithType: t.procedure
    .input(z.object({ branch, language, type, model: z.string() }))
    .output(getMetadataWithTypeOutput)
    .query(async ({ input, ctx }) => {
      const query = `SELECT m.*, l.* FROM ${input.branch}_metadata m 
        INNER JOIN ${input.branch}_metadata_label l ON l.id_metadata = m.id 
        INNER JOIN ${input.branch}_metadata_model mo ON mo.id = m.id_metadata_model 
        WHERE mo.name = $1 AND mo.metadata_type = $2 AND language = $3 ORDER BY rank`;

      const metadata = await ctx.database.manyOrNone(query, [input.model, input.type, input.language]);

      return getMetadataWithTypeOutput.parse(metadata);
    }),
  getMetadataList: t.procedure
    .input(z.object({ branch }))
    .output(getMetadataListOutput)
    .query(async ({ ctx, input }) => {
      const data = await ctx.database.manyOrNone(
        `SELECT id, id_metadata_model, id_list, name, nb::integer FROM ${input.branch}_metadata_list ORDER BY id `,
      );

      return getMetadataListOutput.parse(data);
    }),
  getMetadataListId: t.procedure
    .input(
      z.object({
        branch,
        list_id: id,
      }),
    )
    .output(getMetadataListIdOutput)
    .query(async ({ ctx, input }) => {
      const metadatalistId = await ctx.database.manyOrNone(
        `SELECT * FROM ${input.branch}_metadata_list WHERE id_list = $1 ORDER BY id `,
        [input.list_id],
      );

      return getMetadataListIdOutput.parse(metadatalistId);
    }),
  getMetadataModel: t.procedure
    .input(z.object({ branch, language }))
    .output(getMetadataModelOutput)
    .query(async ({ ctx, input }) => {
      const data = await ctx.database.any(
        `SELECT id, metadata_type, m.name, description
        FROM ${input.branch}_metadata_model m
        INNER JOIN ${input.branch}_metadata_model_label l ON l.id_metadata_model = m.id
        WHERE language = $1 AND visible = 1`,
        input.language,
      );
      return getMetadataModelOutput.parse(data);
    }),
  /**
   * Get all the metadata models used by a project
   * @param branch The affected branch
   * @param language The metadata's label language
   * @param folder_id The id of the project folder
   * @returns The metadata models
   */
  getMetadataModelProject: t.procedure
    .input(z.object({ branch, language, folder_id: id }))
    .output(getMetadataModelProjectOutput.array())
    .query(async ({ input, ctx }) => {
      const models = await ctx.database.one(
        ` SELECT ${input.branch}_get_metadata_modelv4($1, $2) as get_metadata_modelv4 `,
        [input.folder_id, input.language],
      );
      const parsed_models = getMetadataModelProjectOutput.array().parse(models.get_metadata_modelv4.models ?? []);

      if (((input.branch === "pft3d") ||  (input.branch === "corpus") ) && !parsed_models.some((el) => el.metadata_type === "folder")) {
        const folder_models = await ctx.database.manyOrNone(
          `SELECT * FROM ${input.branch}_metadata_model m
          INNER JOIN ${input.branch}_metadata_model_label ml ON m.id = ml.id_metadata_model
          WHERE metadata_type = 'folder' AND language = '${input.language}'`,
        );
        for (const model of folder_models) {
          parsed_models.push(getMetadataModelProjectOutput.parse(model));
        }
      }

      return parsed_models;
  }),
  
  addMetadataValue: t.procedure
  .input(z.object({ branch: z.string(), id_item: z.number(), item_type: z.string(), id_metadata: z.number(),
    root_dir: z.string(), value: z.string().array(), id_user: z.number(), isunique: z.number()}))
  .mutation(async ({input, ctx})=> {

    let isunique = input.isunique ? '1' : '0'
    let value = input.value;
    let val = ''
    let value_stringify = ''
    value = value.map(str => str.replaceAll('\'', ' ').replaceAll(',', ' '));

    if (isunique === '1') {
      val = value[0]
      value_stringify = val;
      console.log(value_stringify)
      if (val !== '') {
        console.log('valeur unique, non null')
        const exists = await ctx.database.oneOrNone(`
        SELECT id
        FROM conservatoire3d_passport
        WHERE id_item=${input.id_item}
        AND item_type='${input.item_type}'
        AND id_metadata=${input.id_metadata}
        AND root_dir='${input.root_dir}'
        AND value[1]=$1`, val)

        if (!exists) {
          ctx.database.none(`
        INSERT INTO ${input.branch}_passport (id_item, item_type, id_metadata, root_dir, value[1], date_created, date_modified, id_user)
        VALUES (${input.id_item}, '${input.item_type}', ${input.id_metadata}, '${input.root_dir}', '${value_stringify}', now(), now(), ${input.id_user})
        ON CONFLICT DO NOTHING`)
        }
      } else if (val === '') { // attention, éviter de vouloir ajouter une valeur nulle !! mais pour les mises à jour ... si on veut supprimer une valeur
        console.log('valeur unique et null ')
        const exists = await ctx.database.oneOrNone(`
          SELECT id
          FROM conservatoire3d_passport
          WHERE id_item=${input.id_item}
          AND item_type='${input.item_type}'
          AND id_metadata=${input.id_metadata}
          AND root_dir='${input.root_dir}' `)

        if (exists) {
          console.log('elle existe on la supprime')
          ctx.database.none(`
        DELETE FROM ${input.branch}_passport
        WHERE id_item = ${input.id_item}
        AND item_type = '${input.item_type}'
        AND id_metadata = ${input.id_metadata}
        AND root_dir = '${input.root_dir}' ` ,)
        } else {
          console.log('elle existe pas et on ne fait rien pour la mettre !!')
        }

      }

    } else { // à revoir
      value_stringify = JSON.stringify(value);
      value_stringify = value_stringify.replaceAll('[', '{').replaceAll(']', '}');
      console.log('fake tableau')
      console.log(value_stringify)
      console.log('valeur non unique')
      const exists = await ctx.database.oneOrNone(`
          SELECT id, value
          FROM conservatoire3d_passport
          WHERE id_item=${input.id_item}
          AND item_type='${input.item_type}'
          AND id_metadata=${input.id_metadata}
          AND root_dir='${input.root_dir}'`)

      if (!exists) {
        ctx.database.none(`
          INSERT INTO ${input.branch}_passport (id_item, item_type, id_metadata, root_dir, value, date_created, date_modified, id_user)
          VALUES (${input.id_item}, '${input.item_type}', ${input.id_metadata}, '${input.root_dir}', '${value_stringify}', now(), now(), ${input.id_user})
          ON CONFLICT (id_item, item_type, id_metadata, root_dir) DO UPDATE SET value = EXCLUDED.value || '${value_stringify}'
        `)
      } else {
        console.log('unique metadata already have value -> we update it with this new value')
        //atention l'anienne valeur est écrasé
        ctx.database.none(`
        UPDATE ${input.branch}_passport  SET value = '${value_stringify}'
        WHERE id_item=${input.id_item} AND item_type='${input.item_type}' AND id_metadata=${input.id_metadata} AND root_dir='${input.root_dir}' `)
      }
    }
  }),

  patchMetadataValue: t.procedure
    .input(patchMetadataValueInput)
    .mutation(async ({ ctx, input }) => {
      let nomDepot = 0; // pour savoir s'il faut mettre à jour le nom du dépôt (fr ou en)
      let nakalaImage = 0; // pour savoir s'il faut mettre à jour l'image dans l'objet (CND3D)
      let nakalaImageValue = "";

    const getRootDir = async (item_type: typeof input.item_type) => {
      if (item_type === "folder") {
        const folder = await ctx.database.oneOrNone(
          `SELECT passport FROM ${input.branch}_folder WHERE id = $1`,
          input.item_id,
        );

        if (!folder) {
          return false;
        }

        return folder.passport;
      }

      if (item_type === "file") {
        const item = await ctx.database.oneOrNone(`SELECT * FROM ${input.branch}_file WHERE id = $1`, input.item_id);

        if (!item) {
          return false;
        }

        // ATTENTION au cas ou le file serait également dans un dossier virtuel (= folder_name NULL) : ne pas le récupérer
        const folder = await ctx.database.oneOrNone(
          `SELECT passport
            FROM ${input.branch}_folder
            WHERE id =
            (SELECT CAST(ltree2text(subpath(folder_path,0,1)) AS INT)
            FROM ${input.branch}_folder fo
            INNER JOIN ${input.branch}_file_folder fio ON fio.id_folder = fo.id
            WHERE id_file = $1 AND folder_name IS NOT NULL)`,
          input.item_id,
        );

        if (!folder) {
          return false;
        }

        return folder.passport;
      }

      if (item_type === "object") {
        // ERREUR CND3D : on prend le parent du folder attaché à l'objet pour récupérer la route ...
        // Or, dans la table des objets , on a une route !(root_dir) il suffit de la récupérer en tant que passport
        // pour coller à la requête précédente des cas folders?
        const folder = await ctx.database.oneOrNone(
          `SELECT root_dir as passport FROM ${input.branch}_object WHERE id = $1`,
          input.item_id,
        );

        if (!folder) {
          return false;
        }

        return folder.passport;
      }
    };

    // récupérer root_dir (= folder_name de la table _folder pour les type folder)
    // todo : root_dir pour les type file ???
    const root_dir = await getRootDir(input.item_type);

    if (!root_dir) {
      return false;
    }

    // on supprime les champs qui mettent opentheso à jour ?
    // champ : 0#132 ou 1#132 selon unicité ou pas (pour tous les champs simple non thesaurus)
    for (const key in input.data) {
      const unique = Number.parseInt(key.split("#")[0]) === 1; // Le champ n'est pas unique on ne fait pas une mise à jour mais un autre insert
      let [_, key_name] = key.split("#");

      if (key_name.includes("opentheso")) {
        continue; // il est indexé par ailleurs ...
      }

      const value = input.data[key];

      //if (!value) { // bug pour les valeurs nulles à supprimer !
      if (typeof value === "undefined") {
        continue;
      }

      if (key_name.startsWith("theso")) {
        // plusieurs cas : theso pactols ou theso periodo ou theso maison
        if (key_name.includes("periodo") && typeof value === "string") {
          // champ = theso_periodo_id
          // value : <id_periodo>_<id_thes_periodo>_<language>
          // example  : 403_p0qhb66qj4c_fr'
          // TODO ATTENTION  : valeur du periodo
          // value: theso_periodo_id: '365_p0qhb66bf45_-0050_0499_fra-latn',
          // value : <id_periodo>_<id_thes_periodo>_<min>_<max>_<language>
          const id_periodo = value.split("_")[0];
          const id_thes_periodo = value.split("_")[1];
          const lang = value.split("_")[4];
          if (value) {
            // on accepte qu'une seule valeur de periodo
            await ctx.thesaurus.upsertPeriodoItem(
              input.branch,
              id_periodo,
              id_thes_periodo,
              input.item_id,
              input.item_type,
              lang,
              input.user_id,
            );
          }
        } else if (key_name.includes("pactols") && typeof value === "string") {
          if (value) {
            // TODO : test récent de patch / ajout d'un tag pactols : voici ce qui est récupéré ...
            // '1#thesopactols--sujet': '12142_26003_15621.13150.26003',
            // champ = thesopactols--<thesaurus>
            // value = thesopactols_<id>_<id_thes>
            // key_name : thesopactols--sujet
            // value :  7394_266327

            const thesaurus_id = value.split("_")[0];
            const id_thes = value.split("_")[1];
            const thesaurus = key_name.split("--")[1];

            // On arrête de comparer les id pur qui peuvent changer si on recharge le thesaurus, seul les id_thes restent inchangés
            const query = `SELECT count(*)
                FROM ${input.branch}_thesaurus_pactols_item
                WHERE thesaurus = $1 AND id_thes_thesaurus = $2 AND id_item = $3 AND item_type = $4`;

            const data = await ctx.database.one(query, [thesaurus, id_thes, input.item_id, input.item_type]);

            if (data.count !== "1") {
              await ctx.thesaurus.insertPactolsItem(
                input.branch,
                {
                  id: input.item_id,
                  type: input.item_type,
                  thesaurus,
                  thesaurus_id,
                  id_thes,
                },
                input.user_id,
              );
            }
          }
        } else {
          // C'est le thesaurus maison
          if (typeof value === "string" && value) {
            // champ = theso_<thesaurus> (avant)
            // champ = theso--<thesaurus>
            // '0#theso--bib_composant': '709_3',
            // value = theso_<id>_<id_thes> (avant)
            // value = <id>_<id_thes>
            const trimmed_value = value.startsWith("theso") ? value.substring(value.indexOf("_") + 1) : value;
            const id_thesaurus = trimmed_value.split("_")[0];
            const id_thes = trimmed_value.split("_")[1];
            //const thesaurus = key_name.split("_")[1];
            const thesaurus = key_name.split("--")[1];

            const { count }: { count: string } = await ctx.database.one(
              `SELECT count(*) FROM ${input.branch}_thesaurus_item WHERE thesaurus = $1 AND id_item = $2 AND item_type = $3 `,
              [thesaurus, input.item_id, input.item_type],
            );

            const item = {
              id: input.item_id,
              type: input.item_type,
              thesaurus,
              thesaurus_id: id_thesaurus,
              id_thes,
            };

            if (count === "0") {
              await ctx.thesaurus.insertItem(input.branch, item, input.user_id);
            } else if (count === "1") {
              // une indexation existe, on fait un update sur l'id_thes qui change
              await ctx.thesaurus.updateItem(input.branch, item, input.user_id);
            }
            // TODO handle reset with no value
          }
        } // fin des champs thesaurus
      } else if (key_name.startsWith("nomen") && typeof value === "string") {
        // TODO : préparer insertion dans la table thesaurus_item
        // champ = nomen--<thesaurus>
        // value = <id>_<id_thes>
        const id_thesaurus = value.split("_")[0];
        const id_thes = value.split("_")[1];
        const thesaurus = key_name.split("--")[1];

        // regle : si on a déjà une valeur pour ce thesaurus pour cet item , on fait un update
        // pour prendre en compte la nouvelle valeur du thesaurus
        const count = await ctx.database.one(
          `SELECT count(*) FROM ${input.branch}_thesaurus_item WHERE thesaurus = $1 AND id_item = $2 AND item_type = $3`,
          [thesaurus, input.item_id, input.item_type],
        );

        const item = {
          id: input.item_id,
          type: input.item_type,
          thesaurus,
          thesaurus_id: id_thesaurus,
          id_thes,
        };
        // nomenclature : 1 seule info possible : si info  déjà présente, on remplace par la nouvelle
        if (count[0].count === "0") {
          await ctx.thesaurus.insertItem(input.branch, item, input.user_id);
        } else if (count[0].count === "1") {
          // une indexation existe, on fait un update sur l'id_thes qui change
          await ctx.thesaurus.updateItem(input.branch, item, input.user_id);
        }
      } else if (key_name.startsWith("multi")) {
        if (input.branch === "conservatoire3d") {
          // TODO : à propager pour les "petits" thesaurus pour Archeogrid Projects
          if (input.update_multi?.length) {
            // il y a des changements à faire
            // update_multi: [ '-deposant_422_2', '-deposant_440_13', '-deposant_430_14', '+deposant_424_6' ]
            for (let u = 0; u < input.update_multi.length; u++) {
              const thesaurus = input.update_multi[u].slice(1).split("_")[0];
              const thesaurus_id = Number.parseInt(input.update_multi[u].slice(1).split("_")[1]);
              const id_thes = Number.parseInt(input.update_multi[u].slice(1).split("_")[2]);
              if (input.update_multi[u].substring(0, 1) === "-") {
                // on supprime -deposant_422_2 (thesaurus, id_thesaurus, id_thes_thesaurus)
                await ctx.thesaurus.deleteMultiItem(
                  input.branch,
                  thesaurus_id,
                  id_thes,
                  thesaurus,
                  input.item_id,
                  input.item_type,
                );
              } else if (input.update_multi[u].substring(0, 1) === "+") {
                await ctx.thesaurus.insertMultiItem(
                  input.branch,
                  {
                    id: input.item_id,
                    type: input.item_type,
                    thesaurus_id,
                    id_thes,
                    thesaurus,
                  },
                  input.user_id,
                );
              }
            }
          }
        } else {
          if (value) {
            // Polyhiérarchie : la valeur récupérée contient le path (en 3eme position) en plus de l'id (qui peut changer en cas de recharge du thesaurus
            // VALUE : [ '422_2', '419_22' ]
            // VALUE : [ '36806_2627_28123.1000.1461.1586.1600.1601.2627', ...]
            let tabValue = [];
            // Maintenant, on peut récupérer un tableau de valeur séparé par des virgules
            //if (value.indexOf(',') !== -1) {
            if (typeof value === "string") {
              tabValue[0] = value;
            } else {
              tabValue = value;
            }
            for (let i = 0; i < tabValue.length; i++) {
              if (!tabValue[i]) continue;
              // TODO : récupérer toutes les valeurs existantes et les supprimer ?
              // TODO comme ça on remet les valeurs demandées et seulement celles-ci ...
              // TODO attention pour archeogrid projet ...
              // ATTENTION ! cela implique que partout dans les formulaires, on a mis en place
              // le select multiple qui permet de récupérer toutes les valeurs
              // Préparer insertion dans la table thesaurus_multi_item
              // champ = multi--<thesaurus>_t_id
              // AVANT : value = <id>_<id_thes>
              // Maintenant: value = <id>_<id_thes>_<path>
              // ou alors si plusieurs item dans le select : on a un tableau de valeurs !
              const thesaurus_id = Number.parseInt(tabValue[i].split("_")[0]);
              const id_thes = Number.parseInt(tabValue[i].split("_")[1]);
              const thes_path = tabValue[i].split("_")[2];
              const thesaurus = key_name.split("--")[1].replace("_t_id", "");
              // regle : thesaurus multi : on ajoute sans regarder si ça existe deja
              // polyhierarchie : on vérifie l'égalité des path (nouvelle colonne dans la table thesaurus_multi_item)
              const count = await ctx.database.one(
                `SELECT count(*)
                  FROM ${input.branch}_thesaurus_multi_item
                  WHERE thesaurus = $1 AND id_item = $2 AND item_type = $3 AND thes_path = $4 `,
                [thesaurus, input.item_id, input.item_type, thes_path],
              );
              if (count.count === "0") {
                await ctx.thesaurus.insertMultiItem(
                  input.branch,
                  {
                    id: input.item_id,
                    type: input.item_type,
                    thesaurus,
                    thesaurus_id,
                    id_thes,
                  },
                  input.user_id,
                );
              }
            }
          }
        }
      } else if (key_name.startsWith("latlng")) {
        // champ = latlng--<id_metadata>
        key_name = key_name.split("--")[1];
        // value = lat,lng exmple : '-21.017854937856104,-39.11132812500001'
        // on récupère value[1] au lieu de value anciennement puisque le champ value est un tableau désormais
        const idpassport = await ctx.database.oneOrNone(
          `SELECT value[1] as value
            FROM ${input.branch}_passport
            WHERE id_item = $1 AND item_type = $2 AND id_metadata = $3 AND root_dir = $4 `,
          [input.item_id, input.item_type, key_name, root_dir],
        );

        if (!idpassport && value) {
          // Aucune donnée présente , on insert les nouvelles données :
          // il faut transformer la chaîne en tableau qui puisse être inséré dans un tableau d'une table postgres :
          const stri = `{"${value}"}`;

          await check_metadata_exists(ctx.database, input.branch, key_name);

          await ctx.database.none(
            `INSERT INTO ${input.branch}_passport (id_item, item_type, id_metadata, root_dir, value, date_created, id_user)
              VALUES ($1, $2, $3, $4, $5 , now(), $6)`,
            [input.item_id, input.item_type, key_name, root_dir, stri, input.user_id],
          );
        } else if (idpassport && value) {
          // nouvelle valeur a remplacer
          if (idpassport.value !== value) {
            // il existe une valeur en base différente de la valeur du formulaire
            // modification de l'update depuis la modification du champ value de text en text[] => value => value[1]
            await check_metadata_exists(ctx.database, input.branch, key_name);

            await ctx.database.none(
              `UPDATE ${input.branch}_passport
                SET value[1] = $1, date_modified = now(), id_user = $2
                WHERE id_item = $3 AND item_type = $4 AND id_metadata = $5 AND root_dir = $6 `,
              [value, input.user_id, input.item_id, input.item_type, key_name, root_dir],
            );
          }
        }
      } else {
        const geo = key_name.startsWith("geonames"); // il faut aller mettre à jour le code geonames du dépôt
        if (geo) key_name = key_name.split("--")[1];
        // Pour les métadonnées de type choix ouvert (choico) pour entrer la valeur dans le questionnaire,
        // il faut lui associer une lettre qu'on supprime maintenant
        if (key_name.includes("_t")) {
          key_name = key_name.replace("_t", "");
        }
        // Si dans les champs on a des non unique : 132_1, 132_2, 132_3 ... C'est qu'on a plusieurs valeurs à rentrer
        //else if (champ.indexOf('_') !== -1) {
        //    champ = champ.split('_')[0] // on a 132, 132, 132
        //}
        if (unique) {
          if (key_name.startsWith("nomDepot")) {
            //if input.branch === 'conservatoire3d'
            nomDepot = 1;
            // champ = nomDepot--<id_metadata>
            key_name = key_name.split("--")[1];
          } else nomDepot = 0;
          // traitement pour l'image Nakala (à mettre aussi dans l'objet en plus du passport de métadonées
          if (key_name.startsWith("NakalaImage") && typeof value === "string") {
            //if input.branch === 'conservatoire3d'
            nakalaImage = 1;
            key_name = key_name.split("--")[1];
            nakalaImageValue = value;
          }

          // on récupère value[1] au lieu de value anciennement puisque le champ value est un tableau désormais
          const idpassport = await ctx.database.oneOrNone(
            `SELECT value[1] as value FROM ${input.branch}_passport WHERE id_item = $1 AND item_type = $2 AND id_metadata = $3 AND root_dir = $4 `,
            [input.item_id, input.item_type, key_name, root_dir],
          );

          if (!idpassport && value) {
            let string_unique = "";
            if (typeof value === "string") {
              if (value.includes(",")) {
                // DANS le cas où il y aurait une virgule dans le text, il faut mettre des "" pour que la valeur
                // ne soit pas considérée comme un tableau avec plusieurs item !
                string_unique = `{"${value.replace(/"/g, '\\"')}"}`;
              } else {
                string_unique = `{${value.replace(/"/g, '\\"')}}`;
              }
              //value = value.split() // c'est la que value devient un tableau
            }
            //    insertPassportNonUnique(input.branch, input.item_id, input.item_type, champ, root_dir, string_unique, userId)
            await check_metadata_exists(ctx.database, input.branch, key_name);

            // Aucune donnée présente , on insert les nouvelles données :
            // il faut transformer la chaîne en tableau qui puisse être inséré dans un tableau d'une table postgres :
            await ctx.database.none(
              `INSERT INTO ${input.branch}_passport (id_item, item_type, id_metadata, root_dir, value, date_created, id_user)
                VALUES ($1, $2, $3, $4, $5 , now(), $6)`,
              [input.item_id, input.item_type, key_name, root_dir, string_unique, input.user_id],
            );
          } else if (idpassport && value !== "") {
            // nouvelle valeur a remplacer
            if (idpassport.value !== value) {
              // il existe une valeur en base différente de la valeur du formulaire
              // modification de l'update depuis la modification du champ value de text en text[] => value => value[1]
              // Pour ne pas avoir plusieurs element en cas de virgule dans la valeur du champ :
              let string_unique = "";
              if (typeof value === "string") {
                if (value.includes(",")) {
                  // DANS le cas où il y aurait une virgule dans le text, il faut mettre des "" pour que la valeur
                  // ne soit pas considérée comme un tableau avec plusieurs item !
                  //string_unique = value.replace(/"/g, '\\"');
                  string_unique = `${value}`;
                } else {
                  string_unique = value;
                }
                //value = value.split() // c'est la que value devient un tableau
              }

              await check_metadata_exists(ctx.database, input.branch, key_name);

              await ctx.database.none(
                `UPDATE ${input.branch}_passport
                  SET value[1] = $1, date_modified = now() , id_user = $2
                  WHERE id_item = $3 AND item_type = $4 AND id_metadata = $5 AND root_dir = $6`,
                [string_unique, input.user_id, input.item_id, input.item_type, key_name, root_dir],
              );

              // si c'est geo il faut faire la mise à jour du dépot
              if (geo) {
                await ctx.database.none("SELECT update_depot_geonames($1, $2, $3, $4)", [
                  input.branch,
                  input.item_type,
                  input.item_id,
                  value,
                ]);
              }
              if (nomDepot) {
                await ctx.database.none("SELECT update_depot_name($1, $2, $3, $4, $5)", [
                  input.branch,
                  input.item_type,
                  input.language,
                  input.item_id,
                  value,
                ]);
              }
              if (nakalaImage) {
                await ctx.database.none(`UPDATE ${input.branch}_object SET id_nakala = $1 WHERE id = $2`, [
                  nakalaImageValue,
                  input.item_id,
                ]);
              }
            }
          } else {
            // value === ''
            if (idpassport && value === "") {
              await ctx.database.none(
                `DELETE FROM ${input.branch}_passport WHERE id_item = $1 AND item_type = $2 AND id_metadata = $3 AND root_dir = $4`,
                [input.item_id, input.item_type, key_name, root_dir],
              );
            }
          }
        } else {
          // if value is unique but type is not array, cast into array
          const non_unique_value = typeof value === "string" ? [value] : value;

          // NON UNIQUE
          // Ce champ n'est pas unique, A traiter différemment:
          // On ajoute les éléments au tableau des valeurs s'il en existe déjà un
          // On fait un insert pour ajouter les elements sinon.
          // A chaque insert on fait attention à ne pas insérer une valeur déjà présente (val <>ALL(value)
          const nbpassport = await ctx.database.oneOrNone(
            `SELECT id, value FROM ${input.branch}_passport WHERE id_item = $1 AND item_type = $2 AND id_metadata = $3 AND root_dir = $4`,
            [input.item_id, input.item_type, key_name, root_dir],
          );

          if (!nbpassport) {
            // pas de valeur existante, on ajoute toutes les valeurs modifiées
            // suppression des doublons si besoin
            const value_array = Array.from(new Set(non_unique_value));

            await check_metadata_exists(ctx.database, input.branch, key_name);

            const insert = `INSERT INTO ${input.branch}_passport(id_item, item_type, id_metadata, root_dir, value, date_created, id_user)
                VALUES ($1, $2, $3, $4, $5, now(), $6)`;

            await ctx.database.none(insert, [
              input.item_id,
              input.item_type,
              key_name,
              root_dir,
              value_array,
              input.user_id,
            ]);

            continue;
          }

          // une valeur existe déjà
          if (input.lot) {
            // on garde la valeur existante
            // on concatène l'ancienne valeur avec la nouvelle (dans le cas de indexation par lot,
            // on ne récupère pas l'ancienne valeur dans le questionnaire)
            // suppression des doublons si besoin
            const totalValue = non_unique_value.concat(nbpassport.value);
            const value_array = Array.from(new Set(totalValue));

            const update_query = `UPDATE ${input.branch}_passport SET value = $2, date_modified = now() WHERE id = $1`;
            await ctx.database.none(update_query, [nbpassport.id, value_array]);

            continue;
          }

          // on écrase la valeur existante
          // on ne récupère pas d'autres valeurs que celles du questionnaire

          // Build values array without duplicates and empty strings
          const values_no_duplicates = Array.from(new Set(non_unique_value.filter(Boolean)));

          // TODO : faire la différence entre les objets de type tableau et les objets de type json
          // par exemple les objets de type json ont des accolades ?
          // c'est une value de type objet avec plusieurs item
          // on met à jour chaque élément du tableau de valeur en le remettant comme on le reçoit

          await check_metadata_exists(ctx.database, input.branch, key_name);

          // 1/ on RAZ le tableau de valeur
          await ctx.database.oneOrNone(
            `UPDATE ${input.branch}_passport
              SET value = string_to_array('', NULL)
              WHERE id_item = $1 AND item_type = $2 AND id_metadata = $3 AND root_dir = $4 `,
            [input.item_id, input.item_type, key_name, root_dir],
          );

          // 2/ on y met les nouvelles valeurs
          for await (const [i, v] of values_no_duplicates.entries()) {
            await ctx.database.none(
              `UPDATE ${input.branch}_passport SET value[${i}] = $2, date_modified = now() WHERE id = $1`,
              [nbpassport.id, v],
            );
          }
        }
      }
    }

    return true;
  }),
  /**
   * V2 of patchMetadataValues
   */
  updateValues: t.procedure.input(updateValuesInput).mutation(async ({ ctx, input }) => {
    const getRootDir = async (item_type: typeof input.item_type) => {
      if (item_type === "folder") {
        const folder = await ctx.database.oneOrNone(
          `SELECT passport FROM ${input.branch}_folder WHERE id = $1`,
          input.item_id,
        );

        if (!folder) {
          return null;
        }

        return folder.passport as string;
      }

      if (item_type === "file") {
        const item = await ctx.database.oneOrNone(`SELECT * FROM ${input.branch}_file WHERE id = $1`, input.item_id);

        if (!item) {
          return null;
        }

        // ATTENTION au cas ou le file serait également dans un dossier virtuel (= folder_name NULL) : ne pas le récupérer
        const folder = await ctx.database.oneOrNone(`
          SELECT passport
          FROM ${input.branch}_folder
          WHERE id = (
            SELECT CAST(ltree2text(subpath(folder_path,0,1)) AS INT)
            FROM ${input.branch}_folder fo
            INNER JOIN ${input.branch}_file_folder fio ON fio.id_folder = fo.id
            WHERE id_file = $1 AND folder_name IS NOT NULL
          )`,input.item_id,
        );

        if (!folder) {
          return null;
        }

        return folder.passport as string;
      }

      if (item_type === "object") {
        // ERREUR CND3D : on prend le parent du folder attaché à l'objet pour récupérer la route ...
        // Or, dans la table des objets , on a une route !(root_dir) il suffit de la récupérer en tant que passport
        // pour coller à la requête précédente des cas folders?
        const folder = await ctx.database.oneOrNone(
          `SELECT root_dir as passport FROM ${input.branch}_object WHERE id = $1`,
          input.item_id,
        );

        if (!folder) {
          return null;
        }

        return folder.passport as string;
      }

      if(item_type === "unico"){
        const unico = await ctx.database.oneOrNone(`SELECT * FROM ${input.branch}_unico WHERE id = $1`, input.item_id);

        if (!unico) {
          return null;
        }

        const folder = await ctx.database.oneOrNone(`
          SELECT passport
          FROM ${input.branch}_folder
          WHERE id = (
            SELECT CAST(ltree2text(subpath(folder_path,0,1)) AS INT)
            FROM ${input.branch}_folder fo
            INNER JOIN ${input.branch}_file_folder fio ON fio.id_folder = fo.id
            WHERE id_file = $1 AND folder_name IS NOT NULL
          )`,unico.id_file,
        );

        if (!folder) {
          return null;
        }

        return folder.passport as string;

      }

      return null;
    };

    // récupérer root_dir (= folder_name de la table _folder pour les type folder)
    // todo : root_dir pour les type file ???
    const root_dir = await getRootDir(input.item_type);

    if (!root_dir) {
      return false;
    }

    // on supprime les champs qui mettent opentheso à jour ?
    // champ : 0#132 ou 1#132 selon unicité ou pas (pour tous les champs simple non thesaurus)
    for (const metadata of input.data) {
      if (metadata.type === "opentheso") {
        continue; // il est indexé par ailleurs ...
      }

      if (metadata.type === "periodo") {
        // champ = theso_periodo_id
        // value : <id_periodo>_<id_thes_periodo>_<language>
        // example  : 403_p0qhb66qj4c_fr'
        // TODO ATTENTION  : valeur du periodo
        // value: theso_periodo_id: '365_p0qhb66bf45_-0050_0499_fra-latn',
        // value : <id_periodo>_<id_thes_periodo>_<min>_<max>_<language>
        const { id_periodo, id_thes_periodo, language } = metadata;

        if (metadata.delete) {
          await ctx.database.none(
            `DELETE FROM ${input.branch}_thesaurus_periodo_item WHERE id_periodo = $1 AND id_item = $2`,
            [id_periodo, input.item_id],
          );
          continue;
        }

        // on accepte qu'une seule valeur de periodo
        const result = await ctx.thesaurus.upsertPeriodoItem(
          input.branch,
          id_periodo,
          id_thes_periodo,
          input.item_id,
          input.item_type,
          language,
          input.user_id,
        );

        if (!result) return false;

        continue;
      }

      if (metadata.type === "pactols") {
        const { thesaurus } = metadata;

        if (metadata.delete) {
          await ctx.database.none(
            `DELETE FROM ${input.branch}_thesaurus_pactols_item WHERE thesaurus = $1 AND id_item = $2 AND item_type = $3`,
            [thesaurus, input.item_id, input.item_type],
          );
          continue;
        }

        const { id_thes, thesaurus_id } = metadata;

        const query = `SELECT count(*)
                FROM ${input.branch}_thesaurus_pactols_item
                WHERE thesaurus = $1 AND id_thes_thesaurus = $2 AND id_item = $3 AND item_type = $4`;

        const data = await ctx.database.one(query, [thesaurus, id_thes, input.item_id, input.item_type]);

        if (data.count !== "1") {
          const result = await ctx.thesaurus.insertPactolsItem(
            input.branch,
            {
              id: input.item_id,
              type: input.item_type,
              thesaurus,
              thesaurus_id,
              id_thes,
            },
            input.user_id,
          );

          if (!result) return false;
        }

        continue;
      }

      if (metadata.type === "thesaurus") {

        const { thesaurus } = metadata;

        if (metadata.delete) {
          await ctx.database.none(
            `DELETE FROM ${input.branch}_thesaurus_item WHERE thesaurus = $1 AND id_item = $2 AND item_type = $3`,
            [thesaurus, input.item_id, input.item_type],
          );
          continue;
        }

        const { id_thes, thesaurus_id } = metadata;

        const count_query: { count: string } = await ctx.database.one(
          `SELECT count(*) FROM ${input.branch}_thesaurus_item WHERE thesaurus = $1 AND id_item = $2 AND item_type = $3 `,
          [thesaurus, input.item_id, input.item_type],
        );

        const exists = Number(count_query.count);

        const item = {
          id: input.item_id,
          type: input.item_type,
          thesaurus,
          thesaurus_id,
          id_thes,
        };

        let result = false;

        if (exists) {
          result = await ctx.thesaurus.updateItem(input.branch, item, input.user_id);
        } else {
          result = await ctx.thesaurus.insertThesaurusItem(
            input.branch,
            'simple',
            metadata.thesaurus_id,
            metadata.id_thes,
            thesaurus,
            input.user_id,
            {
              id_item: input.item_id,
              item_type: input.item_type,
              qualifier: null,
              id_metadata: metadata.id_metadata,
              collection: null
            }
          );
        }

        if (!result) return false;

        continue;
      }

      if (metadata.type === "nomen") {
        const { thesaurus } = metadata;

        if (metadata.delete) {
          await ctx.database.none(
            `DELETE FROM ${input.branch}_thesaurus_item WHERE thesaurus = $1 AND id_item = $2 AND item_type = $3`,
            [thesaurus, input.item_id, input.item_type],
          );
          continue;
        }

        const { id_thes, thesaurus_id } = metadata;

        const count_query: { count: string } = await ctx.database.one(
          `SELECT count(*) FROM ${input.branch}_thesaurus_item WHERE thesaurus = $1 AND id_item = $2 AND item_type = $3`,
          [thesaurus, input.item_id, input.item_type],
        );

        const exists = Number(count_query.count);

        const item = {
          id: input.item_id,
          type: input.item_type,
          thesaurus,
          thesaurus_id,
          id_thes,
        };

        let result = false;

        if (exists) {
          result = await ctx.thesaurus.updateItem(input.branch, item, input.user_id);
        } else {
          result = await ctx.thesaurus.insertThesaurusItem(
            input.branch,
            'simple',
            metadata.thesaurus_id,
            metadata.id_thes,
            thesaurus,
            input.user_id,
            {
              id_item: input.item_id,
              item_type: input.item_type,
              qualifier: null,
              id_metadata: metadata.id_metadata,
              collection: null
            }
          );
        }

        if (!result) return false;

        continue;
      }

      if (metadata.type === "multi") {
        if (input.branch === "conservatoire3d") {
          if (!input.update_multi.length) continue;
          for (let u = 0; u < input.update_multi.length; u++) {
            const thesaurus = input.update_multi[u].slice(1).split("_")[0];
            const thesaurus_id = Number.parseInt(input.update_multi[u].slice(1).split("_")[1]);
            const id_thes = Number.parseInt(input.update_multi[u].slice(1).split("_")[2]);
            if (input.update_multi[u].substring(0, 1) === "-") {
              // on supprime -deposant_422_2 (thesaurus, id_thesaurus, id_thes_thesaurus)
              const result = await ctx.thesaurus.deleteMultiItem(
                input.branch,
                thesaurus_id,
                id_thes,
                thesaurus,
                input.item_id,
                input.item_type,
              );

              if (!result) return false;

              continue;
            }

            if (input.update_multi[u].substring(0, 1) === "+") {

              const result = await ctx.thesaurus.insertThesaurusItem(
                input.branch,
                'multi',
                thesaurus_id,
                id_thes,
                thesaurus,
                input.user_id,
                {
                  id_item: input.item_id,
                  item_type: input.item_type,
                  qualifier: null,
                  id_metadata: metadata.id_metadata,
                  collection: null
                }
              );

              if (!result) return false;
            }
          }

          continue;
        }

        const { thesaurus } = metadata;

        if (metadata.delete) {
          await ctx.database.none(
            `DELETE FROM ${input.branch}_thesaurus_multi_item WHERE thesaurus = $1 AND id_item = $2 AND item_type = $3`,
            [thesaurus, input.item_id, input.item_type],
          );
          continue;
        }

        for (const metadata_value of metadata.values) {
          const { thesaurus_id, thesaurus_path, id_thes } = metadata_value;
          const { count }: { count: string } = await ctx.database.one(
            `SELECT count(*)
                  FROM ${input.branch}_thesaurus_multi_item
                  WHERE thesaurus = $1 AND id_item = $2 AND item_type = $3 AND thes_path = $4 `,
            [thesaurus, input.item_id, input.item_type, thesaurus_path],
          );

          if (!Number(count)) {
            const result = await ctx.thesaurus.insertThesaurusItem(
              input.branch,
              'multi',
              thesaurus_id,
              id_thes,
              thesaurus,
              input.user_id,
              {
                id_item: input.item_id,
                item_type: input.item_type,
                qualifier: null,
                id_metadata: metadata.id_metadata,
                collection: null
              }
            );

            if (!result) return false;
          }
        }

        continue;
      }

      if (metadata.type === "actor") {
        // 1/ On recherche l'acteur
        let id_actor = await ctx.database.oneOrNone(`
          SELECT id, identifier
          FROM ${input.branch}_actor
          WHERE name = '${metadata.actor_literal}'`
        );
        // 2/ On regarde si il faut supprimer sa liaison
        if(metadata.delete) {
          if(!id_actor || !id_actor.id) continue;
          await ctx.database.none(`
            DELETE FROM ${input.branch}_actor_item
            WHERE id_item = $1 AND item_type = $2 AND id_actor = $3 AND id_metadata = $4`, [input.item_id, input.item_type, id_actor.id, metadata.metadata_id]
          );
        }else{
          // 3.1/ Si on veut le lier mais qu'il n'existe pas, on le créer
          if(!id_actor) {
            console.log(`CREATE ACTOR ${metadata.actor_literal} URI ${metadata.actor_identifier}`);
            id_actor = await ctx.database.one(`
              INSERT INTO ${input.branch}_actor (actor_type, name, identifier, creation_date)
              VALUES ($1, $2, $3, now())
              ON CONFLICT ON CONSTRAINT ${input.branch}_unique_actor DO NOTHING
              RETURNING id`, [metadata.actor_type, metadata.actor_literal, metadata.actor_identifier]
            );
          // 3.2/ Si il existe mais qu'on veut modifier son URI
          }else if(id_actor.identifier !== metadata.actor_identifier) {
            console.log(`UPDATE ACTOR ${metadata.actor_literal} URI FROM ${id_actor.identifier} TO ${metadata.actor_identifier}`);
            await ctx.database.none(`
              UPDATE ${input.branch}_actor
              SET identifier = $1
              WHERE id = $2`, [metadata.actor_identifier, id_actor.id]
            );
          }
          // 4/ on ajoute l'association avec l'item
          await ctx.database.none(`
            INSERT INTO ${input.branch}_actor_item (id_item, item_type, id_actor, id_metadata)
            VALUES ($1, $2, $3, $4)
            ON CONFLICT ON CONSTRAINT ${input.branch}_unique_actor_item DO NOTHING`, [input.item_id, input.item_type, id_actor.id, metadata.metadata_id]
          );
        }
        continue;
      }

      if (metadata.type === "datation") {
        // 1/ On recherche la datation
        let id_datation = await ctx.database.oneOrNone(`
          SELECT id
          FROM ${input.branch}_datation
          WHERE date_min = $1
          AND date_max = $2
          AND date_literal = $3
          ${metadata.datation_id_periodo !== "" ? "AND id_periodo = $4": "AND id_periodo IS NULL"}`,
          [
            metadata.datation_date_min,
            metadata.datation_date_max,
            metadata.datation_date_literal,
            metadata.datation_id_periodo
          ]
        );
        // 2/ On regarde si il faut supprimer sa liaison
        if(metadata.delete) {
          // 2.1/ Si la datation n'existe pas, on continue
          if(!id_datation || !id_datation.id) continue;
          // 2.2/ On supprime la liaison
          await ctx.database.none(`
            DELETE FROM ${input.branch}_datation_item
            WHERE id_item = $1 AND item_type = $2 AND id_datation = $3 AND id_metadata = $4`, [input.item_id, input.item_type, id_datation.id, metadata.metadata_id]
          );
          // 2.3/ On regarde si la datation est encore lié à d'autres items
          const remaining_item = await ctx.database.one(`
            SELECT COUNT(id_datation)
            FROM ${input.branch}_datation_item
            WHERE id_datation = $1`, [id_datation.id]
          );
          // 2.4/ Si elle n'est plus lié à aucun item, on la supprime
          if(remaining_item.count === "0"){
            await ctx.database.none(`
              DELETE FROM ${input.branch}_datation
              WHERE id = $1`, [id_datation.id]
            );
          }
        }else{
          // 3/ Si on veut la lier mais qu'elle n'existe pas, on la créer
          if(!id_datation) {

            let date_min_formated: string | null = ctx.datation.formatDate(metadata.datation_date_min, 'min')
            if(!date_min_formated === null){
              console.log('ERROR DATATION DATE MIN CANT BE FORMATED, INSERT NEW DATATION WITH NULL VALUE.');
            }
            let date_max_formated: string | null = ctx.datation.formatDate(metadata.datation_date_max !== "" ? metadata.datation_date_max : metadata.datation_date_min, 'max')
            if(date_max_formated === null){
              console.log('ERROR DATATION DATE MAX CANT BE FORMATED, INSERT NEW DATATION WITH NULL VALUE.');
            }

            id_datation = await ctx.database.one(`
              INSERT INTO ${input.branch}_datation (date_min, date_max, date_literal, id_periodo, date_min_formated, date_max_formated)
              VALUES ($1, $2, $3, $4, $5, $6)
              ON CONFLICT ON CONSTRAINT ${input.branch}_unique_datation DO NOTHING
              RETURNING id`, [
                metadata.datation_date_min,
                metadata.datation_date_max,
                metadata.datation_date_literal,
                metadata.datation_id_periodo || null,
                date_min_formated,
                date_max_formated
              ]
            );
          }
          // 4/ on ajoute l'association avec l'item
          await ctx.database.none(`
            INSERT INTO ${input.branch}_datation_item (id_item, item_type, id_datation, id_metadata)
            VALUES ($1, $2, $3, $4)
            ON CONFLICT ON CONSTRAINT ${input.branch}_unique_datation_item DO NOTHING`
            , [input.item_id, input.item_type, id_datation.id, metadata.metadata_id]
          );
        }
        continue;
      }

      if (metadata.type === "location") {
        // 1/ On vérifie si il y a un code geonames
        let id_location;
        if(metadata.location_uri_geonames !== "" && !isNaN(parseInt(metadata.location_uri_geonames))){
          // 1.1/ On récupère les informations du code geonames
          let geoname = await ctx.database.oneOrNone(`
            SELECT name, latlng
            FROM ${input.branch}_thesaurus_multi
            WHERE thesaurus = 'geo' AND id_thes = $1`, [metadata.location_uri_geonames]
          );
          // 1.2/ Si il n'existe pas, on essaye de le créer
          if(!geoname){
            try{
              const result = await thesaurusController.createGeonamesWithAPI({
                input: { branch: input.branch, id_geonames: metadata.location_uri_geonames},
                ctx,
                path: 'api/metadata',
                type: "mutation",
                rawInput: { branch: input.branch, id_geonames: metadata.location_uri_geonames}
              });

              geoname = result;
              
            }catch(e){
              console.log(e);
              continue;
            }
          }
          // 1.3/ Si il y a eu une erreur, on continue sans le code geonames
          if(!geoname){
            console.log(`CODE GEONAMES (${metadata.location_uri_geonames}) UNFOUND AND CAN'T BE CREATED -> TRYING TO FIND LOCATION WITH NAME AND LONGLAT`);
            if(metadata.location_name === "" && metadata.location_longlat === "") continue;
            id_location = await ctx.database.oneOrNone(`
              SELECT id
              FROM ${input.branch}_location
              WHERE uri_geonames IS NULL AND name = $1 AND longlat = $2`,
              [ metadata.location_name, metadata.location_longlat]
            );
            if(!id_location) continue;
          // 1.4/ Si on a les informations, on remplit les potentiels champs manquants
          }else{
            if(metadata.location_name === "" ) {
              metadata.location_name = geoname.name;
            }
            if(metadata.location_longlat === "" ) {
              metadata.location_longlat = geoname.latlng;
            }
            id_location = await ctx.database.oneOrNone(`
              SELECT id
              FROM ${input.branch}_location
              WHERE uri_geonames = $1 AND name = $2 AND longlat = $3`,
              [ metadata.location_uri_geonames, metadata.location_name, metadata.location_longlat]
            );
          }
        // 2/ On recherche la localisation sans code geonames
        }else{
          id_location = await ctx.database.oneOrNone(`
            SELECT id
            FROM ${input.branch}_location
            WHERE uri_geonames IS NULL AND name = $1 AND longlat = $2`,
            [ metadata.location_name, metadata.location_longlat]
          );
        }
        // 3/ On regarde si il faut supprimer sa liaison
        if(metadata.delete) {
          // 3.1/ On la supprime
          await ctx.database.none(`
            DELETE FROM ${input.branch}_location_item
            WHERE id_item = $1 AND item_type = $2 AND id_location = $3 AND id_metadata = $4`, [input.item_id, input.item_type, id_location.id, metadata.metadata_id]
          );
          // 3.2/ On regarde si la location est encore lié à d'autres items
          const remaining_item = await ctx.database.one(`
            SELECT COUNT(id_location)
            FROM ${input.branch}_location_item
            WHERE id_location = $1`, [id_location.id]
          );
          // 3.3/ Si elle n'est plus lié à aucun item, on la supprime
          if(remaining_item.count === "0"){
            await ctx.database.none(`
              DELETE FROM ${input.branch}_location
              WHERE id = $1`, [id_location.id]
            );
          }
        }else{
          if(!id_location) {
            // 4.1/ Si on veut la lier mais qu'elle n'existe pas, on la créer
            id_location = await ctx.database.oneOrNone(`
              INSERT INTO ${input.branch}_location (name, longlat, uri_geonames)
              VALUES ($1, $2, $3)
              ON CONFLICT ON CONSTRAINT ${input.branch}_unique_location DO NOTHING
              RETURNING id`, [metadata.location_name, metadata.location_longlat, metadata.location_uri_geonames || null]
            );
            // 4.2/ Si on ne peut pas créer la localisation, on passe au suivant
            if(!id_location){
              console.log(`CAN'T CREATE LOCATION ${metadata.location_name} ${metadata.location_longlat} ${metadata.location_uri_geonames}`);
              continue;
            }
          }
          // 5/ On ajoute l'association avec l'item
          await ctx.database.none(`
            INSERT INTO ${input.branch}_location_item (id_item, item_type, id_location, id_metadata)
            VALUES ($1, $2, $3, $4)
            ON CONFLICT ON CONSTRAINT ${input.branch}_unique_location_item DO NOTHING`, [input.item_id, input.item_type, id_location.id, metadata.metadata_id]
          );
        }
        continue;
      }

      if (metadata.delete) {
        await ctx.database.none(
          `DELETE FROM ${input.branch}_passport WHERE id_metadata = $1 AND id_item = $2 AND LOWER(root_dir) = $3`,
          [metadata.metadata_id, input.item_id, root_dir.toLowerCase()],
        );
        continue;
      }

      if (metadata.type === "latlng") {
        const { metadata_id, value } = metadata;
        const idpassport = await ctx.database.oneOrNone(
          `SELECT value[1] as value
            FROM ${input.branch}_passport
            WHERE id_item = $1 AND item_type = $2 AND id_metadata = $3 AND root_dir = $4 LIMIT 1`,
          [input.item_id, input.item_type, metadata_id, root_dir.toLowerCase()],
        );

        await check_metadata_exists(ctx.database, input.branch, metadata_id.toString());

        if (!idpassport) {
          await ctx.database.none(
            `INSERT INTO ${input.branch}_passport (id_item, item_type, id_metadata, root_dir, value, date_created, id_user)
              VALUES ($1, $2, $3, $4, $5, now(), $6)`,
            [input.item_id, input.item_type, metadata_id, root_dir, [value], input.user_id],
          );
          continue;
        }

        await ctx.database.none(
          `UPDATE ${input.branch}_passport
                SET value = $1, date_modified = now(), id_user = $2
                WHERE id_item = $3 AND item_type = $4 AND id_metadata = $5 AND LOWER(root_dir) = $6 `,
          [[value], input.user_id, input.item_id, input.item_type, metadata_id, root_dir.toLowerCase()],
        );

        continue;
      }

      const { metadata_id, value, geo } = metadata;

      if (metadata.is_unique) {
        const idpassport = await ctx.database.oneOrNone(
          `SELECT id, value[1] as value FROM ${input.branch}_passport WHERE id_item = $1 AND item_type = $2 AND id_metadata = $3 AND LOWER(root_dir) = $4 LIMIT 1`,
          [input.item_id, input.item_type, metadata_id, root_dir.toLowerCase()],
        );

        if (!idpassport && value) {
          await check_metadata_exists(ctx.database, input.branch, metadata_id);

          // Aucune donnée présente , on insert les nouvelles données :
          // il faut transformer la chaîne en tableau qui puisse être inséré dans un tableau d'une table postgres :
          await ctx.database.none(
            `INSERT INTO ${input.branch}_passport (id_item, item_type, id_metadata, root_dir, value, date_created, id_user)
                VALUES ($1, $2, $3, $4, $5 , now(), $6)`,
            [input.item_id, input.item_type, metadata_id, root_dir, value, input.user_id],
          );

          continue;
        }

        if (idpassport && value) {
          console.log("existing passport", idpassport);
          // if multiple rows exist, the first one should be selected and other deleted
          await ctx.database.none(
            `DELETE FROM ${input.branch}_passport WHERE id_item = $1 AND item_type = $2 AND id_metadata = $3 AND LOWER(root_dir) = $4 AND id != $5`,
            [input.item_id, input.item_type, metadata_id, root_dir.toLowerCase(), idpassport.id],
          );

          if (idpassport.value !== value) {
            await check_metadata_exists(ctx.database, input.branch, metadata_id);

            await ctx.database.none(
              `UPDATE ${input.branch}_passport
                  SET value = $1, date_modified = now(), id_user = $2
                  WHERE id_item = $3 AND item_type = $4 AND id_metadata = $5 AND LOWER(root_dir) = $6`,
              [value, input.user_id, input.item_id, input.item_type, metadata_id, root_dir.toLowerCase()],
            );

            // si c'est geo il faut faire la mise à jour du dépot
            if (geo) {
              await ctx.database.none("SELECT update_depot_geonames($1, $2, $3, $4)", [
                input.branch,
                input.item_type,
                input.item_id,
                value,
              ]);
            }

            if (metadata.nom_depot) {
              await ctx.database.none("SELECT update_depot_name($1, $2, $3, $4, $5)", [
                input.branch,
                input.item_type,
                input.language,
                input.item_id,
                value,
              ]);
            }

            if (metadata.nakala_image) {
              await ctx.database.none(`UPDATE ${input.branch}_object SET id_nakala = $1 WHERE id = $2`, [
                metadata.nakala_image,
                input.item_id,
              ]);
            }
          }

          continue;
        }

        if (idpassport && !value) {
          await ctx.database.none(
            `DELETE FROM ${input.branch}_passport WHERE id_item = $1 AND item_type = $2 AND id_metadata = $3 AND LOWER(root_dir) = $4`,
            [input.item_id, input.item_type, metadata_id, root_dir.toLowerCase()],
          );
        }

        continue;
      }

      // if value is unique but type is not array, cast into array
      const non_unique_value = typeof value === "string" ? [value] : value;

      // NON UNIQUE
      // Ce champ n'est pas unique, A traiter différemment:
      // On ajoute les éléments au tableau des valeurs s'il en existe déjà un
      // On fait un insert pour ajouter les elements sinon.
      // A chaque insert on fait attention à ne pas insérer une valeur déjà présente (val <>ALL(value)
      const nbpassport = await ctx.database.oneOrNone(
        `SELECT id, value FROM ${input.branch}_passport WHERE id_item = $1 AND item_type = $2 AND id_metadata = $3 AND LOWER(root_dir) = $4 LIMIT 1`,
        [input.item_id, input.item_type, metadata_id, root_dir.toLowerCase()],
      );

      if (!nbpassport) {
        // pas de valeur existante, on ajoute toutes les valeurs modifiées
        // suppression des doublons si besoin
        const value_array = Array.from(new Set(non_unique_value));

        await check_metadata_exists(ctx.database, input.branch, metadata_id);

        const insert = `INSERT INTO ${input.branch}_passport(id_item, item_type, id_metadata, root_dir, value, date_created, id_user)
                VALUES ($1, $2, $3, $4, $5, now(), $6)`;

        await ctx.database.none(insert, [
          input.item_id,
          input.item_type,
          metadata_id,
          root_dir,
          value_array,
          input.user_id,
        ]);

        continue;
      }

      if (input.save) {
        const totalValue = non_unique_value.concat(nbpassport.value);
        const value_array = Array.from(new Set(totalValue));

        const update_query = `UPDATE ${input.branch}_passport SET value = $2, date_modified = now() WHERE id = $1`;
        await ctx.database.none(update_query, [nbpassport.id, value_array]);

        continue;
      }

      const values_no_duplicates = Array.from(new Set(non_unique_value.filter(Boolean)));

      await check_metadata_exists(ctx.database, input.branch, metadata_id);

      // 1/ on RAZ le tableau de valeur
      await ctx.database.oneOrNone(
        `UPDATE ${input.branch}_passport
              SET value = string_to_array('', NULL)
              WHERE id_item = $1 AND item_type = $2 AND id_metadata = $3 AND LOWER(root_dir) = $4 `,
        [input.item_id, input.item_type, metadata_id, root_dir.toLowerCase()],
      );

      // 2/ on y met les nouvelles valeurs
      for (const [i, v] of values_no_duplicates.entries()) {
        await ctx.database.none(
          `UPDATE ${input.branch}_passport SET value[${i}] = $2, date_modified = now() WHERE id = $1`,
          [nbpassport.id, v],
        );
      }
    }

    return false;
  }),
  /**
   * Get all the metadata models used by in the database project
   * @param branch The affected branch
   * @param language The metadata's label language
   * @param folder_id The id of the project folder (useless but mandatory for the postgres request)
   * @returns All the metadata models of the database
   */
  getMetadataModelGeneral: t.procedure
    .input(z.object({ branch, language, folder_id: id }))
    .output(getMetadataModelGeneralOutput.array())
    .query(async ({ input, ctx }) => {
      const models = await ctx.database.one(
        ` SELECT ${input.branch}_get_metadata_modelv2($1, $2) as get_metadata_modelv2 `,
        [input.folder_id, input.language],
      );
      const parsed_models = getMetadataModelGeneralOutput.array().parse(models.get_metadata_modelv2.models ?? []);

      if (input.branch === "pft3d" && !parsed_models.some((el) => el.metadata_type === "folder")) {
        const folder_models = await ctx.database.manyOrNone(
          `SELECT *
          FROM pft3d_metadata_model m
          INNER JOIN pft3d_metadata_model_label ml ON m.id = ml.id_metadata_model
          WHERE metadata_type = 'folder' AND language = '${input.language}'`,
        );
        for (const model of folder_models) {
          parsed_models.push(getMetadataModelGeneralOutput.parse(model));
        }
      }

      return parsed_models;
    }),
  /**
   * Insert default model for projet
   * @param branch The affected branch
   * @param folder_id The id of the project folder
   * @returns folder_id
   */
  createDefaultLinkModelProject: t.procedure
    .input(z.object({ branch: z.string(), folder_id: id }))
    .output(z.boolean())
    .mutation(async ({ input, ctx }) => {
      const linkModelProject = await ctx.database.oneOrNone(
        `SELECT array_agg(id) as default_model from ${input.branch}_metadata_model WHERE name in ('DublinCore', 'DublinCoreUnico', 'exif', 'iptc') `,
      );

      if (!linkModelProject?.default_model) {
        return false;
      }

      // { default_model: [ 3, 4, 11, 28 ] }
      for (let d = 0; d < linkModelProject.default_model.length; d++) {
        await ctx.database.none(`INSERT INTO ${input.branch}_metadata_model_folder VALUES ($1, $2) `, [
          input.folder_id,
          linkModelProject.default_model[d],
        ]);
      }

      return true;
    }),
  createMetadataModel: t.procedure
    .input(
      z.object({
        branch,
        language,
        name: z.string(),
        type: z.union([z.literal("file"), z.literal("folder"), z.literal("object")]),
        desc: z.string(),
        visible: z.number(),
        projects: z.union([z.number(), z.string()]).array().optional(), // projects element can be id_folder or folder_name
      }),
    )
    .mutation(async ({ ctx, input }) => {
      // CREATE MODEL
      const model: { id: number } = await ctx.database.one(
        `INSERT INTO ${input.branch}_metadata_model (metadata_type, name, visible) VALUES ($1, $2, $3) RETURNING id`,
        [input.type, input.name, input.visible],
      );

      // CREATE MODEL LABEL, BOTH LANGUAGES
      await ctx.database.none(
        `INSERT INTO ${input.branch}_metadata_model_label (id_metadata_model, language, label, description)
        VALUES ($1, 'fr', $2, $3)`,
        [model.id, input.name, input.desc],
      );
      await ctx.database.none(
        `INSERT INTO ${input.branch}_metadata_model_label (id_metadata_model, language, label, description)
        VALUES ($1, 'en', $2, $3)`,
        [model.id, input.name, input.desc],
      );
      await ctx.database.none(
        `INSERT INTO ${input.branch}_metadata_model_label (id_metadata_model, language, label, description)
        VALUES ($1, 'am', $2, $3)`,
        [model.id, input.name, input.desc],
      );

      if (input.projects) {
        for (const project_id of input.projects) {
          let id_folder;
          if(typeof project_id === "string"){
            id_folder = (await ctx.database.oneOrNone(`
              SELECT id
              FROM ${input.branch}_folder
              WHERE folder_name = $1`,
              [project_id]
            ))?.id;

            if(!id_folder){
              console.log(`METADATA CONTROLLER WARNING: FOLDER WITH NAME ${project_id} DOES NOT EXIST!`)
              continue;
            }
          }else{
            id_folder = project_id;
          }
          await ctx.database.none(
            `INSERT INTO ${input.branch}_metadata_model_folder (id_model, id_folder)
            VALUES ($1, $2)`,
            [model.id, id_folder],
          );
        }
      }

      return model;
    }),

  createMetadatasOfModel: t.procedure
    .input(
      z.object({
        branch,
        language,
        metadata_model_id: id,
        metadatas : z.array(
          z.object({
            code: z.string(),
            label: z.string(),
            description: z.string(),
            type: z.string(),
            list: z.string().optional(),
            y: z.number(),
            isunique: z.number(),
            rank: z.number(),
            query: z.string()
          })
        )
      }),
    ).mutation(async ({ ctx, input }) => {
      const model_exists = await ctx.database.oneOrNone(
        `SELECT id FROM ${input.branch}_metadata_model WHERE id = $1`,
        input.metadata_model_id,
      );

      if (!model_exists){
        console.log(`METADATA CONTROLLER ERROR: MODEL WITH ID ${input.metadata_model_id} DOES NOT EXIST!`)
        return false;
      }

      for(const metadata of input.metadatas){
        const id_metadata = await ctx.database.one(`
          INSERT INTO ${input.branch}_metadata (id_metadata_model, code, status, y, isunique, rank, query)
          VALUES ($1, $2, $3, $4, $5, $6, $7) RETURNING id`,
          [input.metadata_model_id, metadata.code, metadata.type, metadata.y, metadata.isunique, metadata.rank, metadata.query],
        );

        await ctx.database.none(
          `INSERT INTO ${input.branch}_metadata_label (id_metadata, language, label, description)
          VALUES ($1, 'fr', $2, $3), ($1, 'en', $2, $3), ($1, 'am', $2, $3)`,
          [id_metadata.id, metadata.label, metadata.description],
        );

        if(metadata.list){
          await ctx.database.none(
            `UPDATE ${input.branch}_metadata SET list = $1 WHERE id = $2`,
            [metadata.list, id_metadata.id],
          );
        }
      }

      return true;
    }),

  userModels: t.procedure.input(z.object({ branch, user_id: id })).query(async ({ ctx, input }) => {
    const models = await ctx.database.manyOrNone(
      `SELECT distinct model.* FROM ${input.branch}_metadata_model model
        INNER JOIN ${input.branch}_metadata_model_folder mf ON model.id = mf.id_model
        INNER JOIN ${input.branch}_user_folder uf ON mf.id_folder = uf.id_folder
        WHERE uf.id_user = $1`,
      input.user_id,
    );

    return find_all_output.parse(models);
  }),
  isDeletionPossible: t.procedure.input(z.object({ branch, model_id: id })).query(async ({ ctx, input }) => {
    const model_exists = await ctx.database.oneOrNone(
      `SELECT id FROM ${input.branch}_metadata_model WHERE id = $1`,
      input.model_id,
    );

    if (!model_exists) return false;

    const links = await ctx.database.manyOrNone(
      `SELECT * FROM ${input.branch}_metadata_model_folder WHERE id_model = $1`,
      input.model_id,
    );
    const secondary_links = await ctx.database.manyOrNone(
      `SELECt * FROM ${input.branch}_folder_metadata_model WHERE id_metadata_model = $1`,
      input.model_id,
    );
    return !(links.length + secondary_links.length);
  }),
  patchModel: t.procedure
    .input(
      z.object({
        branch,
        language,
        model_id: id,
        values: z
          .object({
            id: id.nullish(),
            rank: id,
            label: z.string(),
            description: z.string().optional(),
            x: z
              .number()
              .nullable()
              .transform((x) => x || 0),
            query: z.string().nullish(),
            unique: z.boolean(),
            mandatory: z.boolean(),
            optional: z.boolean(),
            type: metadata_type,
            list: z.string().optional(),
            choices: z.string().array(),
            function: z.string().nullish().transform((f) => f === 'all' ? null : f),
          })
          .array(),
      }),
    )
    .output(z.object({ status: z.number(), message: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const ids = input.values.map((v) => v.id).filter(defined);

      const requested_model = await ctx.database.oneOrNone(
        `SELECT name AS model_name FROM ${input.branch}_metadata_model WHERE id = $1`,
        input.model_id,
      );

      if (!requested_model) {
        return { status: 404, message: "Requested model does not exist!"};
      }

      const { model_name } = requested_model;
      let metadataId_query = `SELECT id, status FROM ${input.branch}_metadata WHERE id_metadata_model = $1 `;
      if(ids.length > 0) {
        metadataId_query += `AND id NOT IN (${ids.map((v) => v).join(",")})`;
      }
      const metadataId = await ctx.database.manyOrNone(metadataId_query, [input.model_id]);

      for(const { id, status } of metadataId) {
        let value;
        switch(status) {
          case 'actor':
          case 'datation':
          case 'location':
            value = await ctx.database.oneOrNone(`SELECT * FROM ${input.branch}_${status}_item WHERE id_metadata = $1 LIMIT 1`, [id]);
            break;
          case 'char':
          case 'text':
          case 'integer':
          case 'int':
          case 'date':
          case 'link':
          case 'float':
          case 'json':
          case 'list':
          case 'choice':
          case 'choico': 
            value = await ctx.database.oneOrNone(`SELECT * FROM ${input.branch}_passport WHERE id_metadata = $1               LIMIT 1`, [id]);
            break;
          case 'thesaurus':
            value = await ctx.database.oneOrNone(`SELECT * FROM ${input.branch}_thesaurus_item WHERE id_metadata = $1         LIMIT 1`, [id]);
            break;
          case 'multi':
            value = await ctx.database.oneOrNone(`SELECT * FROM ${input.branch}_thesaurus_multi_item WHERE id_metadata = $1   LIMIT 1`, [id]);
            break;
          case 'pactols':
            value = await ctx.database.oneOrNone(`SELECT * FROM ${input.branch}_thesaurus_pactols_item WHERE id_metadata = $1 LIMIT 1`, [id]);
            break;
          case 'periodo':
            value = await ctx.database.oneOrNone(`SELECT * FROM ${input.branch}_thesaurus_periodo_item WHERE id_metadata = $1 LIMIT 1`, [id]);
            break;
        }
        // Il existe des items qui ont des valeurs pour la métadonnée que l'on veut supprimer
        // On annule et informe l'utilisateur
        if(value){
          console.log(`FIND (${Object.values(value).join(", ")}) FOR METADATA N°${id} IN ${status} TABLE`);
          const error_msg = `Unable to delete metadata n°${id} because it has value. Aborting...`
          console.log(error_msg);
          return {status: 400, message: error_msg};
        }
      }

      if(metadataId.length > 0){
        let delete_label_query =
        `DELETE FROM ${input.branch}_metadata_label ` +
        `WHERE id_metadata IN (SELECT id FROM ${input.branch}_metadata WHERE id_metadata_model = $1) `;

        let delete_histo = `DELETE FROM ${input.branch}_histo WHERE id_metadata IN (${metadataId.map((v) => v.id).join(",")}) `;

        let delete_metadata_query = `DELETE FROM ${input.branch}_metadata WHERE id IN (${metadataId.map((v) => v.id).join(",")}) `;

        let delete_metadata_list_query = `DELETE FROM ${input.branch}_metadata_list WHERE id_metadata_model = $1 `;

        if (ids.length) {
          delete_label_query += "AND id_metadata NOT IN ($2:csv)";
          delete_metadata_list_query += "AND id NOT IN ($2:csv)";
        }

        // delete all metadata in current model which are not included in this input
        await ctx.database.tx(async (tx) => {
          await tx.none(delete_label_query, [input.model_id, ids]);
          await tx.none(delete_histo);
          await tx.none(delete_metadata_query);
          await tx.none(delete_metadata_list_query, [input.model_id, ids]);
        }).catch((e) => {
          console.log(`Error while deleting metadata: ${e.message}`, e);
          const error_msg = `Unable to delete metadata n°${id} because of an error in the database. Aborting...`
          console.log(error_msg);
          return {status: 400, message: error_msg};
        })
      }

      for (const value of input.values) {
        if (value.id) {
          // update existing metadata

          if (value.type === "list" || value.type === "choice" || value.type === "choico") {
            const list_result: { list_id: number } | null = await ctx.database.oneOrNone(
              `SELECT list as list_id FROM ${input.branch}_metadata WHERE id = $1 LIMIT 1`,
              value.id,
            );

            let list_id = list_result?.list_id || null;

            if (list_id) {
              // delete only existing list values for this metadata
              await ctx.database.none(`DELETE FROM ${input.branch}_metadata_list WHERE id_list = $1`, list_id);
            } else if (value.choices.length) {
              // if list_id is null, there are no list values for this metadata, we need a new id to start adding values
              list_id =
                (await ctx.database.one(`SELECT max(id_list) as list_id FROM ${input.branch}_metadata_list`)).list_id +
                1;
            }

            if (!value.choices.length) {
              // if there is no new data, delete the list
              list_id = null;
            }

            for (const choice of value.choices) {
              await ctx.database.none(
                `INSERT INTO ${input.branch}_metadata_list (id_metadata_model, id_list, name, nb) VALUES ($1, $2, $3, 0)`,
                [input.model_id, list_id, choice],
              );
            }

            await ctx.database.none(
              `UPDATE ${input.branch}_metadata
              SET list = $1
              WHERE id = $2`,
              [list_id, value.id],
            );
          }

          console.log("Function =>", value.function)

          await ctx.database.none(
            `UPDATE ${input.branch}_metadata
            SET rank = $1, x = $2, y = $3, status = $4, query = $5, isunique = $6, optional = $7, function = $8
            WHERE id = $9`,
            [
              value.rank,
              value.x,
              value.mandatory ? 1 : 0,
              value.type,
              value.query,
              value.unique ? 1 : 0,
              value.optional,
              value.function,
              value.id
            ],
          );

          await ctx.database.none(
            `UPDATE ${input.branch}_metadata_label
            SET label = $1, description = $2
            WHERE id_metadata = $3`,
            [value.label, value.description ?? "À définir", value.id],
          );
        } else {
          // create metadata

          const slug =
            model_name.substring(0, 2) +
            value.label
              .normalize("NFD")
              .replace(/[\u0300-\u036f]/g, "")
              .trim()
              .replaceAll(" ", "_")
              .toLowerCase();

          let list_id = null;
          if (value.type === "list" || value.type === "choice" || value.type === "choico") {
            list_id =
              (await ctx.database.one(`SELECT max(id_list) as list_id FROM ${input.branch}_metadata_list`)).list_id + 1;
            for await (const choice of value.choices) {
              await ctx.database.none(
                `INSERT INTO ${input.branch}_metadata_list (id_metadata_model, id_list, name, nb)
                VALUES ($1, $2, $3, 0)`,
                [input.model_id, list_id, choice],
              );
            }
          }
          if(value.type === "thesaurus" || value.type === "multi" || value.type === "pactols") {
            list_id = value.list;
          }

          console.log("FUNCTION =>", value.function);

          const new_metadata = await ctx.database.one(
            `INSERT INTO ${input.branch}_metadata (id_metadata_model, rank, x, y, status, list, function, query, code, isunique, optional)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
            RETURNING id`,
            [
              input.model_id,
              value.rank,
              value.x,
              value.mandatory ? 1 : 0,
              value.type,
              list_id,
              value.function,
              "y",
              slug,
              value.unique ? 1 : 0,
              value.optional,

            ],
          );

          const description = value.description || "À définir";

          await ctx.database.none(
            `INSERT INTO ${input.branch}_metadata_label (id_metadata, language, label, description)
            VALUES ($1, 'fr', $2, $3)`,
            [new_metadata.id, value.label, description],
          );
          await ctx.database.none(
            `INSERT INTO ${input.branch}_metadata_label (id_metadata, language, label, description)
            VALUES ($1, 'en', $2, $3)`,
            [new_metadata.id, value.label, description],
          );
          await ctx.database.none(
            `INSERT INTO ${input.branch}_metadata_label (id_metadata, language, label, description)
            VALUES ($1, 'am', $2, $3)`,
            [new_metadata.id, value.label, description],
          );
        }
      }

      return {status: 200, message: "ok"};
    }),

  deleteModel: t.procedure.input(z.object({ branch, model_id: id })).mutation(async ({ ctx, input }) => {
    const model_exists = await ctx.database.oneOrNone(
      `SELECT id FROM ${input.branch}_metadata_model WHERE id = $1`,
      input.model_id,
    );

    if (!model_exists) return false;

    const { existing_metadata } = await ctx.database.one(
      `SELECT count(*) as existing_metadata FROM ${input.branch}_metadata WHERE id_metadata_model = $1`,
      input.model_id,
    );

    if (Number.parseInt(existing_metadata) > 0) {
      return false;
    }

    await ctx.database.none(
      `DELETE FROM ${input.branch}_metadata_model_label WHERE id_metadata_model = $1`,
      input.model_id,
    );
    await ctx.database.none(`DELETE FROM ${input.branch}_metadata_model WHERE id = $1`, input.model_id);
    await ctx.database.none(`DELETE FROM ${input.branch}_metadata_model WHERE id = $1`, input.model_id);

    return true;
  }),

  deleteModelWithValues: t.procedure.input(z.object({ branch, model_id: id })).mutation(async ({ ctx, input }) => {
    ctx.metadata.deleteMetadataModel(input.model_id, input.branch);
  }),

  getMetadataModelOfItem: t.procedure
  .input(z.object({ branch, id_item: id, item_type: z.enum(["folder", "object"]) }))
  .output(z.string().nullable())
  .query(async ({ ctx, input }) => {
    let model;
    switch(input.item_type) {
      case "folder":
        model = await ctx.database.oneOrNone(`
          SELECT mm.name
          FROM ${input.branch}_folder_metadata_model fmm
          INNER JOIN ${input.branch}_metadata_model mm ON fmm.id_metadata_model = mm.id
          WHERE fmm.id_folder = ${input.id_item}
          LIMIT 1`
        )
        return model?.name ?? null;
      case "object":
        const res = await ctx.database.oneOrNone(`
          SELECT object_type
          FROM ${input.branch}_object
          WHERE id = $1`,
          [input.id_item]
        );

        switch(res.object_type) {
          case 'virtual':
            return 'virtualObjectV2';
          case 'physical':
            return 'physicalObject';
          default:
            return 'virtualObject';
        }
    }
  }),

  InMetadataForKeywords: t.procedure
  .input(z.object({ branch, id_item: id, item_type: z.string(), language: z.string() }))
  .query(async ({ ctx, input }) => {
    return await ctx.database.manyOrNone(`
      SELECT mm.name as modelname, mm.metadata_type as modeltype, m.*, ml.label
      FROM ${input.branch}_metadata m
      INNER JOIN ${input.branch}_metadata_model mm ON mm.id = m.id_metadata_model
      INNER JOIN ${input.branch}_passport p ON m.id = p.id_metadata
      INNER JOIN ${input.branch}_metadata_label ml ON m.id = ml.id_metadata
      WHERE p.id_item = $1
      AND p.item_type = $2
      AND m.status IN ('char', 'text')
      AND ml.language = $3`, [input.id_item, input.item_type, input.language]);
  }),

  InMetadataForKeywordsIndexMulti: t.procedure
  .input(z.object({ branch, id_folder: id, language: z.string() }))
  .output(z.object({
    id: z.number(),
    code: z.string().nullable(),
    status: z.string(),
    label: z.string(),
    modelid: z.number(),
    modelname: z.string(),
    modeltype: z.string(),
  }).array())
  .query(async ({ ctx, input }) => {
    return await ctx.database.manyOrNone(`
      (
          SELECT DISTINCT m.id, m.code, m.status, ml.label, mm.id as modelid, mm.name as modelname, mm.metadata_type as modeltype
          FROM ${input.branch}_metadata m
          INNER JOIN ${input.branch}_metadata_label ml ON ml.id_metadata = m.id
          INNER JOIN ${input.branch}_metadata_model mm ON mm.id = m.id_metadata_model
          INNER JOIN ${input.branch}_passport p ON p.id_metadata = m.id
          INNER JOIN ${input.branch}_file fi ON fi.id = p.id_item AND p.item_type = 'file' AND fi.id_folder = $1
      ) UNION (
          SELECT DISTINCT m.id, m.code, m.status, ml.label, mm.id as modelid, mm.name as modelname, mm.metadata_type as modeltype
          FROM ${input.branch}_metadata m
          INNER JOIN ${input.branch}_metadata_label ml ON ml.id_metadata = m.id
          INNER JOIN ${input.branch}_metadata_model mm ON mm.id = m.id_metadata_model
          INNER JOIN ${input.branch}_passport p ON p.id_metadata = m.id
          INNER JOIN ${input.branch}_object o ON p.id_item = o.id AND p.item_type = 'object' AND o.id_folder = $1
      ) UNION (
          SELECT DISTINCT m.id, m.code, m.status, ml.label, mm.id as modelid, mm.name as modelname, mm.metadata_type as modeltype
          FROM ${input.branch}_metadata m
          INNER JOIN ${input.branch}_metadata_label ml ON ml.id_metadata = m.id
          INNER JOIN ${input.branch}_metadata_model mm ON mm.id = m.id_metadata_model
          INNER JOIN ${input.branch}_passport p ON p.id_metadata = m.id
          INNER JOIN ${input.branch}_folder fo ON p.id_item = fo.id AND p.item_type = 'folder' AND fo.id_parent = $1
      ) UNION (
          SELECT DISTINCT m.id, m.code, m.status, ml.label, mm.id as modelid, mm.name as modelname, mm.metadata_type as modeltype
          FROM ${input.branch}_metadata m
          INNER JOIN ${input.branch}_metadata_label ml ON ml.id_metadata = m.id
          INNER JOIN ${input.branch}_metadata_model mm ON mm.id = m.id_metadata_model
          INNER JOIN ${input.branch}_passport p ON p.id_metadata = m.id
          INNER JOIN ${input.branch}_unico u ON p.id_item = u.id AND p.item_type = 'unico' AND u.id IN
              (
                  SELECT fu.id_unico
                  FROM ${input.branch}_folder_unico fu
                  WHERE id_folder = $1
              )
      )
      ORDER BY modeltype`, [input.id_folder, input.language]);
  }),

  OutmetadataForKeywords: t.procedure
  .input(z.object({ branch, item_type: z.string().optional(), language: z.string() }))
  .query(async ({ ctx, input }) => {
    return await ctx.database.manyOrNone(`
      SELECT mm.name as modelname, mm.metadata_type as modeltype, m.id, m.list, ml.label, CASE WHEN m.status = 'thesaurus' THEN 'simple' ELSE m.status END as thesaurus_type
      FROM ${input.branch}_metadata_model mm
      INNER JOIN ${input.branch}_metadata m ON mm.id = m.id_metadata_model
      INNER JOIN ${input.branch}_metadata_label ml ON m.id = ml.id_metadata
      WHERE ml.language = $1
      ${input.item_type ? `AND mm.metadata_type = $2` : ''}
      AND m.status IN ('multi', 'thesaurus', 'pactols')`, [input.language, input.item_type]);
  }),

  /**
   * Copy the metadata from one item to another
   * @param from_item The item from which we copy the metadata
   * @param to_item The item to which we paste the metadata
   * @param language The current language of the user
   * @param conflict_mode The mode to use in case of conflict between the new and the old metadata (add, overwrite, ignore) - 06/2025 default is ignore, may be extended in the future
   * @returns An object with the status of the operation and an array of objects with the type, label, success and error of each metadata
   */
  copypasteMetadataItem: t.procedure
  .input(z.object({ from_item: copypasteItem, to_item: copypasteItem, language: z.enum(['fr', 'en']).default('fr'), conflict_mode: z.enum(['add', 'overwrite', 'ignore']).default('ignore') }))
  .output(z.object({ status: z.string(), details: z.array(z.object({ type: z.string(), label: z.string(), success: z.boolean(), error: z.string().optional() }))}))
  .mutation(async ({ ctx, input }) => {
    const { from_item, to_item, language, conflict_mode } = input;
    // Vérification des types d'item
    if(from_item.item_type !== to_item.item_type) {
      return {
        status: 'error',
        details: [
          {
            type: 'item_type',
            label: '',
            success: false,
            error: 'Les types d\'item ne correspondent pas'
          }
        ]
      };
    }
    // Centralise la logique de conflit pour extension future
    function shouldCopyPasteMetadata(existing: any | null, incoming: any, isUnique: boolean, mode: string) {
      if (!existing) return true;
      if (mode === 'overwrite') return true;
      if (mode === 'ignore') return false;
      // mode === 'add'
      if (!isUnique) return JSON.stringify(existing.value) !== JSON.stringify(incoming);
      return false;
    }
    // Fonction d'aide pour récupérer le label d'une métadonnée
    async function getMetadataLabel(id_metadata: number, branch: string, language: string): Promise<string> {
      // Essaie de récupérer l'étiquette à partir de metadata_label
      const labelRow = await ctx.database.oneOrNone(
        `SELECT label FROM ${branch}_metadata_label WHERE id_metadata = $1 AND language = $2`,
        [id_metadata, language]
      );
      if (labelRow?.label) return labelRow.label;
      // Récupère le code à partir de metadata
      const codeRow = await ctx.database.oneOrNone(
        `SELECT code FROM ${branch}_metadata WHERE id = $1`,
        [id_metadata]
      );
      return codeRow?.code || '';
    }

    // Tableau pour stocker les détails du copier-coller
    const outputDetails: Array<{ type: string, label: string, success: boolean, error?: string }> = [];

    // 1. Passport
    const passportPromise = (async () => {
      try {
        const fromPassports = await ctx.database.manyOrNone(
          `SELECT * FROM ${from_item.branch}_passport WHERE id_item = $1 AND item_type = $2`,
          [from_item.item_id, from_item.item_type]
        );
        const toPassports = await ctx.database.manyOrNone(
          `SELECT * FROM ${to_item.branch}_passport WHERE id_item = $1 AND item_type = $2`,
          [to_item.item_id, to_item.item_type]
        );
        const results = await Promise.all(fromPassports.map(async (meta) => {
          try {
            const metaDef = await ctx.database.oneOrNone(
              `SELECT isunique FROM ${from_item.branch}_metadata WHERE id = $1`, [meta.id_metadata]
            );
            const isUnique = metaDef?.isunique === 1;
            const existing = toPassports.find(p => p.id_metadata === meta.id_metadata);
            if (shouldCopyPasteMetadata(existing, meta.value, isUnique, conflict_mode)) {
              await ctx.database.none(
                `INSERT INTO ${to_item.branch}_passport (id_item, item_type, id_metadata, root_dir, value, date_created, id_user) VALUES ($1, $2, $3, $4, $5, now(), $6)`,
                [to_item.item_id, to_item.item_type, meta.id_metadata, meta.root_dir, meta.value, meta.id_user]
              );
            }
            const label = await getMetadataLabel(meta.id_metadata, from_item.branch, language);
            return { type: 'passport', label, success: true };
          } catch (err: any) {
            const label = await getMetadataLabel(meta.id_metadata, from_item.branch, language);
            return { type: 'passport', label, success: false, error: err?.message || String(err) };
          }
        }));
        outputDetails.push(...results);
      } catch (err: any) {
        outputDetails.push({ type: 'passport', label: '', success: false, error: err?.message || String(err) });
      }
    })();
    // 2. Actor
    const actorPromise = (async () => {
      try {
        // Récupérer tous les liens actor-metadata pour from_item
        const fromActors = await ctx.database.manyOrNone(
          `SELECT * FROM ${from_item.branch}_actor_item WHERE id_item = $1 AND item_type = $2`,
          [from_item.item_id, from_item.item_type]
        );
        // Récupérer tous les liens actor-metadata pour to_item
        const toActors = await ctx.database.manyOrNone(
          `SELECT * FROM ${to_item.branch}_actor_item WHERE id_item = $1 AND item_type = $2`,
          [to_item.item_id, to_item.item_type]
        );
        // Pour chaque lien actor-metadata du from_item
        const results = await Promise.all(fromActors.map(async (meta) => {
          try {
            // Vérifier unicité de la métadonnée (isunique)
            const metaDef = await ctx.database.oneOrNone(
              `SELECT isunique FROM ${from_item.branch}_metadata WHERE id = $1`, [meta.id_metadata]
            );
            const isUnique = metaDef?.isunique === 1;
            // Chercher s'il existe déjà un lien équivalent
            const existing = toActors.find(p => p.id_metadata === meta.id_metadata && p.id_actor === meta.id_actor);
            // Gestion du mode de conflit
            if (shouldCopyPasteMetadata(existing, meta.id_actor, isUnique, conflict_mode)) {
              // Si overwrite, on supprime les liens existants pour cette métadonnée
              if (conflict_mode === 'overwrite' && isUnique) {
                await ctx.database.none(
                  `DELETE FROM ${to_item.branch}_actor_item WHERE id_item = $1 AND item_type = $2 AND id_metadata = $3`,
                  [to_item.item_id, to_item.item_type, meta.id_metadata]
                );
              }
              // On insère le lien si non existant
              if (!existing) {
                await ctx.database.none(
                  `INSERT INTO ${to_item.branch}_actor_item (id_item, item_type, id_actor, id_metadata) VALUES ($1, $2, $3, $4)`,
                  [to_item.item_id, to_item.item_type, meta.id_actor, meta.id_metadata]
                );
              }
            }
            const label = await getMetadataLabel(meta.id_metadata, from_item.branch, language);
            return { type: 'actor', label, success: true };
          } catch (err: any) {
            const label = await getMetadataLabel(meta.id_metadata, from_item.branch, language);
            return { type: 'actor', label, success: false, error: err?.message || String(err) };
          }
        }));
        outputDetails.push(...results);
      } catch (err: any) {
        outputDetails.push({ type: 'actor', label: '', success: false, error: err?.message || String(err) });
      }
    })();
    // 3. Datation
    const datationPromise = (async () => {
      try {
        const fromDatations = await ctx.database.manyOrNone(
          `SELECT * FROM ${from_item.branch}_datation_item WHERE id_item = $1 AND item_type = $2`,
          [from_item.item_id, from_item.item_type]
        );
        const toDatations = await ctx.database.manyOrNone(
          `SELECT * FROM ${to_item.branch}_datation_item WHERE id_item = $1 AND item_type = $2`,
          [to_item.item_id, to_item.item_type]
        );
        const results = await Promise.all(fromDatations.map(async (meta) => {
          try {
            const metaDef = await ctx.database.oneOrNone(
              `SELECT isunique FROM ${from_item.branch}_metadata WHERE id = $1`, [meta.id_metadata]
            );
            const isUnique = metaDef?.isunique === 1;
            const existing = toDatations.find(p => p.id_metadata === meta.id_metadata && p.id_datation === meta.id_datation);
            if (shouldCopyPasteMetadata(existing, meta.id_datation, isUnique, conflict_mode)) {
              if (conflict_mode === 'overwrite' && isUnique) {
                await ctx.database.none(
                  `DELETE FROM ${to_item.branch}_datation_item WHERE id_item = $1 AND item_type = $2 AND id_metadata = $3`,
                  [to_item.item_id, to_item.item_type, meta.id_metadata]
                );
              }
              if (!existing) {
                await ctx.database.none(
                  `INSERT INTO ${to_item.branch}_datation_item (id_item, item_type, id_datation, id_metadata) VALUES ($1, $2, $3, $4)`,
                  [to_item.item_id, to_item.item_type, meta.id_datation, meta.id_metadata]
                );
              }
            }
            const label = await getMetadataLabel(meta.id_metadata, from_item.branch, language);
            return { type: 'datation', label, success: true };
          } catch (err: any) {
            const label = await getMetadataLabel(meta.id_metadata, from_item.branch, language);
            return { type: 'datation', label, success: false, error: err?.message || String(err) };
          }
        }));
        outputDetails.push(...results);
      } catch (err: any) {
        outputDetails.push({ type: 'datation', label: '', success: false, error: err?.message || String(err) });
      }
    })();
    // 4. Location
    const locationPromise = (async () => {
      try {
        const fromLocations = await ctx.database.manyOrNone(
          `SELECT * FROM ${from_item.branch}_location_item WHERE id_item = $1 AND item_type = $2`,
          [from_item.item_id, from_item.item_type]
        );
        const toLocations = await ctx.database.manyOrNone(
          `SELECT * FROM ${to_item.branch}_location_item WHERE id_item = $1 AND item_type = $2`,
          [to_item.item_id, to_item.item_type]
        );
        const results = await Promise.all(fromLocations.map(async (meta) => {
          try {
            const metaDef = await ctx.database.oneOrNone(
              `SELECT isunique FROM ${from_item.branch}_metadata WHERE id = $1`, [meta.id_metadata]
            );
            const isUnique = metaDef?.isunique === 1;
            const existing = toLocations.find(p => p.id_metadata === meta.id_metadata && p.id_location === meta.id_location);
            if (shouldCopyPasteMetadata(existing, meta.id_location, isUnique, conflict_mode)) {
              if (conflict_mode === 'overwrite' && isUnique) {
                await ctx.database.none(
                  `DELETE FROM ${to_item.branch}_location_item WHERE id_item = $1 AND item_type = $2 AND id_metadata = $3`,
                  [to_item.item_id, to_item.item_type, meta.id_metadata]
                );
              }
              if (!existing) {
                await ctx.database.none(
                  `INSERT INTO ${to_item.branch}_location_item (id_item, item_type, id_location, id_metadata) VALUES ($1, $2, $3, $4)`,
                  [to_item.item_id, to_item.item_type, meta.id_location, meta.id_metadata]
                );
              }
            }
            const label = await getMetadataLabel(meta.id_metadata, from_item.branch, language);
            return { type: 'location', label, success: true };
          } catch (err: any) {
            const label = await getMetadataLabel(meta.id_metadata, from_item.branch, language);
            return { type: 'location', label, success: false, error: err?.message || String(err) };
          }
        }));
        outputDetails.push(...results);
      } catch (err: any) {
        outputDetails.push({ type: 'location', label: '', success: false, error: err?.message || String(err) });
      }
    })();
    // 5. Inventory
    const inventoryPromise = (async () => {
      try {
        const fromInventories = await ctx.database.manyOrNone(
          `SELECT * FROM ${from_item.branch}_inventory_item WHERE id_item = $1 AND item_type = $2`,
          [from_item.item_id, from_item.item_type]
        );
        const toInventories = await ctx.database.manyOrNone(
          `SELECT * FROM ${to_item.branch}_inventory_item WHERE id_item = $1 AND item_type = $2`,
          [to_item.item_id, to_item.item_type]
        );
        const results = await Promise.all(fromInventories.map(async (meta) => {
          try {
            const metaDef = await ctx.database.oneOrNone(
              `SELECT isunique FROM ${from_item.branch}_metadata WHERE id = $1`, [meta.id_metadata]
            );
            const isUnique = metaDef?.isunique === 1;
            const existing = toInventories.find(p => p.id_metadata === meta.id_metadata && p.id_inventory === meta.id_inventory);
            if (shouldCopyPasteMetadata(existing, meta.id_inventory, isUnique, conflict_mode)) {
              if (conflict_mode === 'overwrite' && isUnique) {
                await ctx.database.none(
                  `DELETE FROM ${to_item.branch}_inventory_item WHERE id_item = $1 AND item_type = $2 AND id_metadata = $3`,
                  [to_item.item_id, to_item.item_type, meta.id_metadata]
                );
              }
              if (!existing) {
                await ctx.database.none(
                  `INSERT INTO ${to_item.branch}_inventory_item (id_item, item_type, id_inventory, id_metadata) VALUES ($1, $2, $3, $4)`,
                  [to_item.item_id, to_item.item_type, meta.id_inventory, meta.id_metadata]
                );
              }
            }
            const label = await getMetadataLabel(meta.id_metadata, from_item.branch, language);
            return { type: 'inventory', label, success: true };
          } catch (err: any) {
            const label = await getMetadataLabel(meta.id_metadata, from_item.branch, language);
            return { type: 'inventory', label, success: false, error: err?.message || String(err) };
          }
        }));
        outputDetails.push(...results);
      } catch (err: any) {
        outputDetails.push({ type: 'inventory', label: '', success: false, error: err?.message || String(err) });
      }
    })();
    // 6. Thesaurus
    const thesaurusPromise = (async () => {
      try {
        const fromThesaurus = await ctx.database.manyOrNone(
          `SELECT * FROM ${from_item.branch}_thesaurus_item WHERE id_item = $1 AND item_type = $2`,
          [from_item.item_id, from_item.item_type]
        );
        const toThesaurus = await ctx.database.manyOrNone(
          `SELECT * FROM ${to_item.branch}_thesaurus_item WHERE id_item = $1 AND item_type = $2`,
          [to_item.item_id, to_item.item_type]
        );
        const results = await Promise.all(fromThesaurus.map(async (meta) => {
          try {
            // Unicité par couple (thesaurus, id_metadata)
            const metaDef = await ctx.database.oneOrNone(
              `SELECT isunique FROM ${from_item.branch}_metadata WHERE id = $1`, [meta.id_metadata]
            );
            const isUnique = metaDef?.isunique === 1;
            const existing = toThesaurus.find(p => p.thesaurus === meta.thesaurus && p.id_metadata === meta.id_metadata);
            if (shouldCopyPasteMetadata(existing, meta.id_thes, isUnique, conflict_mode)) {
              if (conflict_mode === 'overwrite' && isUnique) {
                await ctx.database.none(
                  `DELETE FROM ${to_item.branch}_thesaurus_item WHERE id_item = $1 AND item_type = $2 AND thesaurus = $3 AND id_metadata = $4`,
                  [to_item.item_id, to_item.item_type, meta.thesaurus, meta.id_metadata]
                );
              }
              if (!existing) {
                await ctx.database.none(
                  `INSERT INTO ${to_item.branch}_thesaurus_item (id_thesaurus, thesaurus, id_thes_thesaurus, id_item, item_type, public, id_user, date_modified, thes_path, qualifier, id_metadata) VALUES ($1, $2, $3, $4, $5, $6, $7, now(), $8, $9, $10)`,
                  [meta.id_thesaurus, meta.thesaurus, meta.id_thes_thesaurus, to_item.item_id, to_item.item_type, meta.public, meta.id_user, meta.thes_path, meta.qualifier, meta.id_metadata]
                );
              }
            }
            const label = await getMetadataLabel(meta.id_metadata, from_item.branch, language);
            return { type: 'thesaurus', label, success: true };
          } catch (err: any) {
            const label = await getMetadataLabel(meta.id_metadata, from_item.branch, language);
            return { type: 'thesaurus', label, success: false, error: err?.message || String(err) };
          }
        }));
        outputDetails.push(...results);
      } catch (err: any) {
        outputDetails.push({ type: 'thesaurus', label: '', success: false, error: err?.message || String(err) });
      }
    })();
    // 7. Thesaurus_multi
    const thesaurusMultiPromise = (async () => {
      try {
        const fromMulti = await ctx.database.manyOrNone(
          `SELECT * FROM ${from_item.branch}_thesaurus_multi_item WHERE id_item = $1 AND item_type = $2`,
          [from_item.item_id, from_item.item_type]
        );
        const toMulti = await ctx.database.manyOrNone(
          `SELECT * FROM ${to_item.branch}_thesaurus_multi_item WHERE id_item = $1 AND item_type = $2`,
          [to_item.item_id, to_item.item_type]
        );
        const results = await Promise.all(fromMulti.map(async (meta) => {
          try {
            const metaDef = await ctx.database.oneOrNone(
              `SELECT isunique FROM ${from_item.branch}_metadata WHERE id = $1`, [meta.id_metadata]
            );
            const isUnique = metaDef?.isunique === 1;
            const existing = toMulti.find(p => p.thesaurus === meta.thesaurus && p.id_metadata === meta.id_metadata && p.thes_path === meta.thes_path);
            if (shouldCopyPasteMetadata(existing, meta.id_thes, isUnique, conflict_mode)) {
              if (conflict_mode === 'overwrite' && isUnique) {
                await ctx.database.none(
                  `DELETE FROM ${to_item.branch}_thesaurus_multi_item WHERE id_item = $1 AND item_type = $2 AND thesaurus = $3 AND id_metadata = $4 AND thes_path = $5`,
                  [to_item.item_id, to_item.item_type, meta.thesaurus, meta.id_metadata, meta.thes_path]
                );
              }
              if (!existing) {
                await ctx.database.none(
                  `INSERT INTO ${to_item.branch}_thesaurus_multi_item (id_thesaurus, thesaurus, id_thes_thesaurus, id_item, item_type, public, id_user, date_modified, thes_path, qualifier, id_metadata) VALUES ($1, $2, $3, $4, $5, $6, $7, now(), $8, $9, $10)`,
                  [meta.id_thesaurus, meta.thesaurus, meta.id_thes_thesaurus, to_item.item_id, to_item.item_type, meta.public, meta.id_user, meta.thes_path, meta.qualifier, meta.id_metadata]
                );
              }
            }
            const label = await getMetadataLabel(meta.id_metadata, from_item.branch, language);
            return { type: 'thesaurus_multi', label, success: true };
          } catch (err: any) {
            const label = await getMetadataLabel(meta.id_metadata, from_item.branch, language);
            return { type: 'thesaurus_multi', label, success: false, error: err?.message || String(err) };
          }
        }));
        outputDetails.push(...results);
      } catch (err: any) {
        outputDetails.push({ type: 'thesaurus_multi', label: '', success: false, error: err?.message || String(err) });
      }
    })();
    // 8. Thesaurus_pactols
    const thesaurusPactolsPromise = (async () => {
      try {
        const fromPactols = await ctx.database.manyOrNone(
          `SELECT * FROM ${from_item.branch}_thesaurus_pactols_item WHERE id_item = $1 AND item_type = $2`,
          [from_item.item_id, from_item.item_type]
        );
        const toPactols = await ctx.database.manyOrNone(
          `SELECT * FROM ${to_item.branch}_thesaurus_pactols_item WHERE id_item = $1 AND item_type = $2`,
          [to_item.item_id, to_item.item_type]
        );
        const results = await Promise.all(fromPactols.map(async (meta) => {
          try {
            const metaDef = await ctx.database.oneOrNone(
              `SELECT isunique FROM ${from_item.branch}_metadata WHERE id = $1`, [meta.id_metadata]
            );
            const isUnique = metaDef?.isunique === 1;
            const existing = toPactols.find(p => p.thesaurus === meta.thesaurus && p.id_metadata === meta.id_metadata && p.id_thes_thesaurus === meta.id_thes_thesaurus);
            if (shouldCopyPasteMetadata(existing, meta.id_thes, isUnique, conflict_mode)) {
              if (conflict_mode === 'overwrite' && isUnique) {
                await ctx.database.none(
                  `DELETE FROM ${to_item.branch}_thesaurus_pactols_item WHERE id_item = $1 AND item_type = $2 AND thesaurus = $3 AND id_metadata = $4 AND id_thes_thesaurus = $5`,
                  [to_item.item_id, to_item.item_type, meta.thesaurus, meta.id_metadata, meta.id_thes_thesaurus]
                );
              }
              if (!existing) {
                await ctx.database.none(
                  `INSERT INTO ${to_item.branch}_thesaurus_pactols_item (id_thesaurus, thesaurus, id_thes_thesaurus, id_item, item_type, public, id_user, date_modified, qualifier, collection, id_metadata) VALUES ($1, $2, $3, $4, $5, $6, $7, now(), $8, $9, $10)`,
                  [meta.id_thesaurus, meta.thesaurus, meta.id_thes_thesaurus, to_item.item_id, to_item.item_type, meta.public, meta.id_user, meta.qualifier, meta.collection, meta.id_metadata]
                );
              }
            }
            const label = await getMetadataLabel(meta.id_metadata, from_item.branch, language);
            return { type: 'thesaurus_pactols', label, success: true };
          } catch (err: any) {
            const label = await getMetadataLabel(meta.id_metadata, from_item.branch, language);
            return { type: 'thesaurus_pactols', label, success: false, error: err?.message || String(err) };
          }
        }));
        outputDetails.push(...results);
      } catch (err: any) {
        outputDetails.push({ type: 'thesaurus_pactols', label: '', success: false, error: err?.message || String(err) });
      }
    })();
    // 9. Thesaurus_periodo
    const thesaurusPeriodoPromise = (async () => {
      try {
        const fromPeriodo = await ctx.database.manyOrNone(
          `SELECT * FROM ${from_item.branch}_thesaurus_periodo_item WHERE id_item = $1 AND item_type = $2`,
          [from_item.item_id, from_item.item_type]
        );
        const toPeriodo = await ctx.database.manyOrNone(
          `SELECT * FROM ${to_item.branch}_thesaurus_periodo_item WHERE id_item = $1 AND item_type = $2`,
          [to_item.item_id, to_item.item_type]
        );
        const results = await Promise.all(fromPeriodo.map(async (meta) => {
          try {
            const metaDef = await ctx.database.oneOrNone(
              `SELECT isunique FROM ${from_item.branch}_metadata WHERE id = $1`, [meta.id_metadata]
            );
            const isUnique = metaDef?.isunique === 1;
            const existing = toPeriodo.find(p => p.id_thes_periodo === meta.id_thes_periodo && p.id_metadata === meta.id_metadata && p.qualifier === meta.qualifier);
            if (shouldCopyPasteMetadata(existing, meta.id_periodo, isUnique, conflict_mode)) {
              if (conflict_mode === 'overwrite' && isUnique) {
                await ctx.database.none(
                  `DELETE FROM ${to_item.branch}_thesaurus_periodo_item WHERE id_item = $1 AND item_type = $2 AND id_thes_periodo = $3 AND id_metadata = $4 AND qualifier ${meta.qualifier ? '= $5' : 'IS NULL'}`,
                  meta.qualifier ? [to_item.item_id, to_item.item_type, meta.id_thes_periodo, meta.id_metadata, meta.qualifier] : [to_item.item_id, to_item.item_type, meta.id_thes_periodo, meta.id_metadata]
                );
              }
              if (!existing) {
                await ctx.database.none(
                  `INSERT INTO ${to_item.branch}_thesaurus_periodo_item (id_periodo, id_thes_periodo, id_item, item_type, language, public, id_user, date_modified, qualifier, id_metadata) VALUES ($1, $2, $3, $4, $5, $6, $7, now(), $8, $9)`,
                  [meta.id_periodo, meta.id_thes_periodo, to_item.item_id, to_item.item_type, meta.language, meta.public, meta.id_user, meta.qualifier, meta.id_metadata]
                );
              }
            }
            const label = await getMetadataLabel(meta.id_metadata, from_item.branch, language);
            return { type: 'thesaurus_periodo', label, success: true };
          } catch (err: any) {
            const label = await getMetadataLabel(meta.id_metadata, from_item.branch, language);
            return { type: 'thesaurus_periodo', label, success: false, error: err?.message || String(err) };
          }
        }));
        outputDetails.push(...results);
      } catch (err: any) {
        outputDetails.push({ type: 'thesaurus_periodo', label: '', success: false, error: err?.message || String(err) });
      }
    })();
    // 10. Tag
    const tagPromise = (async () => {
      try {
        const fromTags = await ctx.database.manyOrNone(
          `SELECT * FROM ${from_item.branch}_item_tag WHERE id_item = $1 AND item_type = $2`,
          [from_item.item_id, from_item.item_type]
        );
        const toTags = await ctx.database.manyOrNone(
          `SELECT * FROM ${to_item.branch}_item_tag WHERE id_item = $1 AND item_type = $2`,
          [to_item.item_id, to_item.item_type]
        );
        const results = await Promise.all(fromTags.map(async (meta) => {
          try {
            // Unicité sur id_tag + id_metadata
            const existing = toTags.find(p => p.id_tag === meta.id_tag && p.id_metadata === meta.id_metadata);
            // Les tags ne sont jamais uniques (on peut avoir plusieurs tags différents)
            if (shouldCopyPasteMetadata(existing, meta.id_tag, false, conflict_mode)) {
              if (conflict_mode === 'overwrite') {
                await ctx.database.none(
                  `DELETE FROM ${to_item.branch}_item_tag WHERE id_item = $1 AND item_type = $2 AND id_tag = $3 AND id_metadata = $4`,
                  [to_item.item_id, to_item.item_type, meta.id_tag, meta.id_metadata]
                );
              }
              if (!existing) {
                await ctx.database.none(
                  `INSERT INTO ${to_item.branch}_item_tag (id_item, item_type, id_tag, id_metadata, date_tagged) VALUES ($1, $2, $3, $4, now())`,
                  [to_item.item_id, to_item.item_type, meta.id_tag, meta.id_metadata]
                );
              }
            }
            const label = await getMetadataLabel(meta.id_metadata, from_item.branch, language);
            return { type: 'tag', label, success: true };
          } catch (err: any) {
            const label = await getMetadataLabel(meta.id_metadata, from_item.branch, language);
            return { type: 'tag', label, success: false, error: err?.message || String(err) };
          }
        }));
        outputDetails.push(...results);
      } catch (err: any) {
        outputDetails.push({ type: 'tag', label: '', success: false, error: err?.message || String(err) });
      }
    })();

    // Exécution parallèle
    await Promise.allSettled([
      passportPromise,
      actorPromise,
      datationPromise,
      locationPromise,
      inventoryPromise,
      thesaurusPromise,
      thesaurusMultiPromise,
      thesaurusPactolsPromise,
      thesaurusPeriodoPromise,
      tagPromise
    ]);
    // Statut global
    const allSuccess = outputDetails.every(d => d.success);
    let status: 'success' | 'partial' | 'error';
    if (allSuccess) status = 'success';
    else if (outputDetails.some(d => d.success)) status = 'partial';
    else status = 'error';
    return { status, details: outputDetails };
  }),
  
  metadataStatusList: t.procedure
  .input(z.object({branch}))
  .output(z.string().array())
  .query(async ({ctx, input}) =>{
    return metadata_type.options.map((literal) => literal.value);
  }),

  getFirstMetadata: t.procedure
  .input(z.object({
    branch,
    item_id: id,
    item_type: z.string(),
    model: z.string().optional()
  }))
  .output(z.object({
    title: z.string().nullable()
  }).nullable())
  .query(async ({ input, ctx }) => {
    // Only show detailed logs for the first item (669 based on your example)
    const isFirstItem = input.item_id === 669;
    
    try {
      const allMetadata = await ctx.database.manyOrNone(
        `SELECT
            p.value[1] as value,
            mm.name as model_name,
            mm.id as model_id
         FROM ${input.branch}_passport p
         JOIN ${input.branch}_metadata m ON p.id_metadata = m.id
         JOIN ${input.branch}_metadata_model mm ON m.id_metadata_model = mm.id
         WHERE p.id_item = $1 AND p.item_type = $2
         ORDER BY m.rank ASC`,
        [input.item_id, input.item_type]
      );

      if (!allMetadata || allMetadata.length === 0) {
        if (isFirstItem) console.log(`[METADATA DEBUG] Item ${input.item_id}: No metadata found at all.`);
        return { title: null };
      }

      const metadataMap = new Map<string, string>();
      for (const m of allMetadata) {
        if (!metadataMap.has(m.model_name) && m.value) {
          metadataMap.set(m.model_name, m.value);
        }
      }
      if (isFirstItem) console.log(`[METADATA DEBUG] Item ${input.item_id}: Found metadata for models:`, Array.from(metadataMap.keys()));

      // If a specific model is requested, try it first
      if (input.model && metadataMap.has(input.model) && metadataMap.get(input.model) !== '') {
          if (isFirstItem) console.log(`[METADATA DEBUG] Item ${input.item_id}: SUCCESS with specified model ${input.model}: "${metadataMap.get(input.model)}"`);
          return { title: metadataMap.get(input.model) };
      }

      // Get the default model from file_passport
      let folderInfo: { file_passport: string } | null = null;
      if (input.item_type === 'folder') {
        folderInfo = await ctx.database.oneOrNone(`
            SELECT file_passport FROM ${input.branch}_folder WHERE id = $1
        `, [input.item_id]);
      } else {
        folderInfo = await ctx.database.oneOrNone(`
            SELECT f.file_passport
            FROM ${input.branch}_folder f
            JOIN ${input.branch}_${input.item_type} i ON f.id = i.id_folder
            WHERE i.id = $1
        `, [input.item_id]);
      }

      const defaultModel = folderInfo?.file_passport || 'DublinCore';
      if (isFirstItem) console.log(`[METADATA DEBUG] Item ${input.item_id}: Default model is ${defaultModel}.`);

      // Try default model
      if (metadataMap.has(defaultModel) && metadataMap.get(defaultModel) !== '') {
          if (isFirstItem) console.log(`[METADATA DEBUG] Item ${input.item_id}: SUCCESS with default model ${defaultModel}: "${metadataMap.get(defaultModel)}"`);
          return { title: metadataMap.get(defaultModel) };
      }
      
      // Try DublinCore as a fallback
      if (defaultModel !== 'DublinCore' && metadataMap.has('DublinCore') && metadataMap.get('DublinCore') !== '') {
          if (isFirstItem) console.log(`[METADATA DEBUG] Item ${input.item_id}: SUCCESS with DublinCore fallback: "${metadataMap.get('DublinCore')}"`);
          return { title: metadataMap.get('DublinCore') };
      }

      // Try any other model that has a value
      for (const [model, value] of metadataMap.entries()) {
          if (value && value !== '') {
              if (isFirstItem) console.log(`[METADATA DEBUG] Item ${input.item_id}: SUCCESS with other model ${model}: "${value}"`);
              return { title: value };
          }
      }
      
      if (isFirstItem) console.log(`[METADATA DEBUG] Item ${input.item_id}: All models failed, returning null`);
      return { title: null };

    } catch (error) {
      console.error('Error in getFirstMetadata V2:', error);
      
      // Return null instead of throwing to maintain compatibility
      return { title: null };
    }
  })
});
